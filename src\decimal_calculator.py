# decimal_calculator.py
# 使用decimal模块进行精确的小数计算，避免浮点数精度问题

from decimal import Decimal, getcontext, localcontext, ROUND_DOWN
import pandas as pd
import math

# 设置全局精度为7位有效数字
getcontext().prec = 7

def to_decimal(value):
    """
    将输入值转换为Decimal类型
    :param value: 输入值（可能是float, int, str或None）
    :return: Decimal对象或None
    """
    if value is None or pd.isna(value):
        return None
    try:
        return Decimal(str(value))
    except (ValueError, TypeError):
        return Decimal('0')

def to_float(decimal_value):
    """
    将Decimal值转换为float类型（用于与pandas兼容）
    :param decimal_value: Decimal对象
    :return: float值
    """
    if decimal_value is None:
        return 0.0
    return float(decimal_value)

def precise_truncate_to_decimals(value, decimals):
    """
    使用decimal模块精确截断到指定小数位数
    :param value: 要截断的数值
    :param decimals: 保留的小数位数
    :return: 截断后的值（float类型，保持与现有代码兼容）
    """
    if decimals is None or decimals < 0:
        return value
    
    # 使用局部上下文避免影响全局设置
    with localcontext() as ctx:
        ctx.prec = 28  # 使用更高精度进行中间计算
        ctx.rounding = ROUND_DOWN  # 向下截断
        
        decimal_value = to_decimal(value)
        if decimal_value is None:
            return value
        
        # 计算截断因子
        factor = Decimal('10') ** decimals
        
        # 执行截断：先乘以因子，向下取整，再除以因子
        truncated = (decimal_value * factor).to_integral_value(rounding=ROUND_DOWN) / factor
        
        return to_float(truncated)

def calculate_wire_residual(wire_length, loss_rate, decimals):
    """
    计算导线的精确残值
    :param wire_length: 导线长度
    :param loss_rate: 损耗率
    :param decimals: 最小有效值小数位数
    :return: (截断后的值, 残值)
    """
    with localcontext() as ctx:
        ctx.prec = 28  # 使用更高精度进行中间计算
        ctx.rounding = ROUND_DOWN
        
        # 转换为Decimal类型
        length_decimal = to_decimal(wire_length)
        rate_decimal = to_decimal(loss_rate)
        
        if length_decimal is None or rate_decimal is None:
            return 0.0, 0.0
        
        # 计算原始分子：长度 × (1 + 损耗率)
        raw_molecule = length_decimal * (Decimal('1') + rate_decimal)
        
        # 计算截断值
        if decimals > 0:
            factor = Decimal('10') ** decimals
            truncated_molecule = (raw_molecule * factor).to_integral_value(rounding=ROUND_DOWN) / factor
        else:
            truncated_molecule = raw_molecule.to_integral_value(rounding=ROUND_DOWN)
        
        # 计算残值
        residual = raw_molecule - truncated_molecule
        
        return to_float(truncated_molecule), to_float(residual)

def calculate_sleeve_residual(total_length, unit_length, loss_rate, decimals):
    """
    计算套管的精确残值
    :param total_length: 套管总长度
    :param unit_length: 系统单位长度
    :param loss_rate: 损耗率
    :param decimals: 最小有效值小数位数
    :return: (截断后的值, 残值)
    """
    with localcontext() as ctx:
        ctx.prec = 28  # 使用更高精度进行中间计算
        ctx.rounding = ROUND_DOWN
        
        # 转换为Decimal类型
        total_decimal = to_decimal(total_length)
        unit_decimal = to_decimal(unit_length)
        rate_decimal = to_decimal(loss_rate)
        
        if total_decimal is None or rate_decimal is None:
            return 0.0, 0.0
        
        # 计算原始值
        if unit_decimal is not None and unit_decimal > 0:
            raw_value = (total_decimal * (Decimal('1') + rate_decimal)) / unit_decimal
        else:
            raw_value = total_decimal * (Decimal('1') + rate_decimal)
        
        # 计算截断值
        if decimals > 0:
            factor = Decimal('10') ** decimals
            truncated_value = (raw_value * factor).to_integral_value(rounding=ROUND_DOWN) / factor
        else:
            truncated_value = raw_value.to_integral_value(rounding=ROUND_DOWN)
        
        # 计算残值
        residual = raw_value - truncated_value
        
        return to_float(truncated_value), to_float(residual)

def precise_floor_division(dividend, divisor, decimals):
    """
    精确的向下取整除法
    :param dividend: 被除数
    :param divisor: 除数
    :param decimals: 保留的小数位数
    :return: 向下取整后的结果
    """
    with localcontext() as ctx:
        ctx.prec = 28
        ctx.rounding = ROUND_DOWN
        
        dividend_decimal = to_decimal(dividend)
        divisor_decimal = to_decimal(divisor)
        
        if dividend_decimal is None or divisor_decimal is None or divisor_decimal == 0:
            return 0.0
        
        # 执行除法
        result = dividend_decimal / divisor_decimal
        
        # 截断到指定小数位数
        if decimals > 0:
            factor = Decimal('10') ** decimals
            truncated_result = (result * factor).to_integral_value(rounding=ROUND_DOWN) / factor
        else:
            truncated_result = result.to_integral_value(rounding=ROUND_DOWN)
        
        return to_float(truncated_result)

def validate_decimal_precision():
    """
    验证decimal模块的精度设置
    :return: 当前精度设置
    """
    return getcontext().prec

def test_decimal_calculations():
    """
    测试decimal计算的准确性
    """
    print("=== Decimal计算模块测试 ===")
    print(f"当前精度设置: {validate_decimal_precision()}位有效数字")
    
    # 测试截断函数
    test_value = 123.456789
    truncated = precise_truncate_to_decimals(test_value, 3)
    print(f"截断测试: {test_value} -> {truncated} (保留3位小数)")
    
    # 测试导线残值计算
    wire_length = 100.123456
    loss_rate = 0.05
    truncated_wire, residual_wire = calculate_wire_residual(wire_length, loss_rate, 2)
    print(f"导线残值测试: 长度={wire_length}, 损耗率={loss_rate}")
    print(f"  截断值={truncated_wire}, 残值={residual_wire}")
    
    # 测试套管残值计算
    total_length = 50.789123
    unit_length = 1.5
    truncated_sleeve, residual_sleeve = calculate_sleeve_residual(total_length, unit_length, loss_rate, 2)
    print(f"套管残值测试: 总长度={total_length}, 单位长度={unit_length}, 损耗率={loss_rate}")
    print(f"  截断值={truncated_sleeve}, 残值={residual_sleeve}")
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    test_decimal_calculations()
