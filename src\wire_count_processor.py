# wire_count_processor.py

import pandas as pd
import os
import numpy as np
from collections import defaultdict
import logging

# 导入日志配置
from logger_config import get_wire_count_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info

# 获取导线统计处理日志记录器
logger = get_wire_count_logger()
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志

printer_connections = {}  # 格式: {(屏柜,PP): set(接线点)}

def process_wire_color(df):
    """
    处理颜色/线径标识列
    注意：多芯线处理已移至专门的多芯线统计模块，此函数不再生成四芯线和两芯线记录
    """
    # 按屏柜编号和设备类型分组
    grouped = df.groupby(['屏柜编号', '设备类型（起点/终点）'])

    # 存储处理结果
    processed_rows = []

    for (cabinet, device), group in grouped:
        # 存储当前组的处理结果
        group_rows = group.to_dict('records')

        # 提取特殊线型（多芯线相关颜色）
        multi_core_colors = {'棕($)', '蓝($)', '黑($)', '花($)'}

        # 存储非多芯线相关的线型
        other_wires = []

        # 分离多芯线相关颜色和其他线型
        for row in group_rows:
            wire_type = row['颜色/线径标识']
            if wire_type not in multi_core_colors:
                # 非多芯线相关颜色，保留原样
                other_wires.append(row)
            # 多芯线相关颜色（棕($)、蓝($)、黑($)、花($)）不再在此处理，
            # 由多芯线统计模块专门处理

        # 添加处理后的行
        processed_rows.extend(other_wires)

    # 创建新的DataFrame
    return pd.DataFrame(processed_rows)

def process_wire_count(wiring_df):
    try:
        log_function_start(logger, "process_wire_count")
        df = wiring_df.copy()
        log_data_info(logger, "输入数据", len(df))

        # 创建打印机数据线记录表
        printer_df = pd.DataFrame(columns=[
            '屏柜编号',
            '设备类型（起点/终点）',
            '导线起点',
            '导线终点',
            '导线种类',
            '固定长度',
            '对应编码'
        ])

        # 创建电源线记录表
        power_wire_df = pd.DataFrame(columns=['屏柜编号', '设备类型（起点/终点）', '导线起点', '导线终点'])

        printer_indices = []  # 存储打印机数据线索引
        power_indices = []  # 存储电源线索引

        # 存储每个打印机的接线点信息
        printer_connections = {}  # 格式: {(屏柜编号, PP标识): set(接线点)}

        # 第一步：收集所有打印机的接线点信息
        for idx, row in df.iterrows():
            start = str(row['导线起点'])
            if 'PP' in start:
                # 提取PP标识 (如"1PP")
                pp_id = start.split(':')[0]
                cabinet = row['屏柜编号']
                key = (cabinet, pp_id)

                # 初始化该打印机的接线点集合
                if key not in printer_connections:
                    printer_connections[key] = set()

                # 添加当前行的接线点
                printer_connections[key].add(row['导线起点'])

        # 第二步：根据接线点数量确定导线种类
        printer_wire_types = {}
        for key, points in printer_connections.items():
            point_count = len(points)
            if point_count <= 2:
                printer_wire_types[key] = '两芯线'
            else:  # 3-4个点
                printer_wire_types[key] = '四芯线'

        # 第三步：处理打印机数据线
        for idx, row in df.iterrows():
            start = str(row['导线起点'])
            if 'PP' in start:
                pp_id = start.split(':')[0]
                cabinet = row['屏柜编号']
                key = (cabinet, pp_id)

                # 获取该打印机的导线种类
                wire_type = printer_wire_types.get(key, '两芯线')

                printer_df.loc[len(printer_df)] = [
                    cabinet,
                    row['设备类型（起点/终点）'],
                    row['导线起点'],
                    row['导线终点'],
                    wire_type,
                    2.5,
                    ''
                ]
                printer_indices.append(idx)
                # 标记为打印机数据线
                df.at[idx, '颜色/线径标识'] = f"数据线({pp_id})"

        # 处理电源线
        def check_power_wires(row):
            if 'PRT' in str(row['导线起点']):
                power_indices.append(row.name)
                power_wire_df.loc[len(power_wire_df)] = [
                    row['屏柜编号'],
                    row['设备类型（起点/终点）'],
                    row['导线起点'],
                    row['导线终点']
                ]
                return "电源线"
            return row['颜色/线径标识']

        # 应用电源线检查
        df['颜色/线径标识'] = df.apply(check_power_wires, axis=1)

        # 处理屏蔽层
        def check_shield_layer(row):
            if row.name in printer_indices:
                return row['颜色/线径标识']
            if ('屏蔽层' in str(row['导线起点']) or
                    '屏蔽层' in str(row['导线终点'])):
                return '屏蔽层'
            return row['颜色/线径标识']

        # 应用屏蔽层检查
        df['颜色/线径标识'] = df.apply(check_shield_layer, axis=1)

        # 处理空值
        for idx, row in df.iterrows():
            if idx in printer_indices or idx in power_indices:
                continue
            if pd.isna(row['颜色/线径标识']) or row['颜色/线径标识'] == '':
                d_value = row.get('设备类型（起点/终点）', '')
                if '接地铜排' in str(d_value):
                    df.at[idx, '颜色/线径标识'] = '花(**)'
                else:
                    df.at[idx, '颜色/线径标识'] = '未标识'

        # 分组统计（排除打印机数据线和电源线）
        exclude_indices = printer_indices + power_indices
        non_exclude_df = df[~df.index.isin(exclude_indices)]
        grouped = non_exclude_df.groupby(['屏柜编号', '设备类型（起点/终点）', '颜色/线径标识'])
        result = grouped.size().reset_index(name='导线根数')
        result.columns = ['屏柜编号', '设备类型（起点/终点）', '颜色/线径标识', '导线根数']

        # 空值处理完毕后，添加对应线径列（初始化为空）
        result.insert(3, '对应线径', None)  # 在颜色/线径标识右侧新增列

        result = process_wire_color(result)

        return result, printer_df, power_wire_df

    except Exception as e:
        logger.exception("处理导线根数统计时发生错误")
        raise

def merge_power_wire_records(power_wire_df):
    """
    合并电源线记录，按屏柜编号分组，处理导线起点和终点格式，根数统计为每组不同导线起点"/"左侧设备标号的数量
    :param power_wire_df: 原始电源线记录DataFrame
    :return: 合并后的DataFrame，包含根数统计
    """
    if power_wire_df.empty:
        return power_wire_df
    
    # 注释所有print语句
    # print("开始处理电源线记录合并...")
    
    merged_records = []
    
    # 按屏柜编号和对应编码分组
    grouped = power_wire_df.groupby(['屏柜编号', '对应编码'])
    for (cabinet, code), group in grouped:
        if pd.isna(cabinet) or pd.isna(code):
            continue
        # 统计唯一设备标号
        device_set = set()
        for start_point in group['导线起点']:
            if pd.isna(start_point):
                continue
            left_part = str(start_point).split('/')[0]
            if ':' in left_part:
                left_part = left_part.split(':')[0]
            device_set.add(left_part.strip())
        root_count = len(device_set)
        # 取第一条记录的其他信息
        first_row = group.iloc[0]
        merged_records.append({
            '屏柜编号': cabinet,
            '设备类型（起点/终点）': first_row['设备类型（起点/终点）'] if '设备类型（起点/终点）' in first_row else '',
            '导线起点': first_row['导线起点'],
            '导线终点': first_row['导线终点'],
            '对应编码': code,
            '根数': root_count
        })
        # print(f"屏柜={cabinet}, 编码={code}, 唯一设备标号={device_set}, 根数={root_count}")
    
    if merged_records:
        result_df = pd.DataFrame(merged_records)
        # print(f"合并完成: 原始{len(power_wire_df)}条 → 合并后{len(result_df)}条")
        return result_df
    else:
        # print("合并完成: 无有效记录")
        return pd.DataFrame()

def process_printer_wire_format(printer_df):
    """
    处理数据线记录表的导线起点和终点格式
    :param printer_df: 原始数据线记录DataFrame
    :return: 处理后的DataFrame
    """
    if printer_df.empty:
        return printer_df
    
    # 注释所有print语句
    # print("开始处理数据线记录表格式...")
    
    # 创建处理后的DataFrame副本
    processed_df = printer_df.copy()
    
    for idx, row in processed_df.iterrows():
        start_point = str(row['导线起点'])
        end_point = str(row['导线终点'])
        
        # 处理导线起点：以"/"为分界，取左右两侧，删除":"及其右侧内容
        if '/' in start_point:
            left_part = start_point.split('/')[0]
            right_part = start_point.split('/')[1] if len(start_point.split('/')) > 1 else ''
            
            # 删除":"及其右侧内容
            if ':' in left_part:
                left_part = left_part.split(':')[0]
            if ':' in right_part:
                right_part = right_part.split(':')[0]
            
            processed_start = f"{left_part}/{right_part}"
        else:
            # 如果没有"/"，只处理":"右侧内容
            if ':' in start_point:
                processed_start = start_point.split(':')[0]
            else:
                processed_start = start_point
        
        # 处理导线终点：以"/"为分界，取左右两侧，删除":"及其右侧内容
        if '/' in end_point:
            left_part = end_point.split('/')[0]
            right_part = end_point.split('/')[1] if len(end_point.split('/')) > 1 else ''
            
            # 删除":"及其右侧内容
            if ':' in left_part:
                left_part = left_part.split(':')[0]
            if ':' in right_part:
                right_part = right_part.split(':')[0]
            
            processed_end = f"{left_part}/{right_part}"
        else:
            # 如果没有"/"，只处理":"右侧内容
            if ':' in end_point:
                processed_end = end_point.split(':')[0]
            else:
                processed_end = end_point
        
        # 更新DataFrame中的值
        processed_df.at[idx, '导线起点'] = processed_start
        processed_df.at[idx, '导线终点'] = processed_end
        
        # print(f"  记录{idx}: 起点={start_point} → {processed_start}, 终点={end_point} → {processed_end}")
    
    # print(f"数据线记录表格式处理完成: {len(processed_df)}条记录")
    return processed_df