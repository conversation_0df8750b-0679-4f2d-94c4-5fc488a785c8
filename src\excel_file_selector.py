import tkinter as tk
from tkinter import ttk, filedialog, Frame, Canvas, Scrollbar, VERTICAL, messagebox
import os
import json
from pathlib import Path
from design_doc_processor import DesignDocProcessor


class ExcelFileSelector:
    # 配置文件路径：确保在脚本所在目录下
    CONFIG_FILE = Path(__file__).parent / "excel_processor_config.json"

    def __init__(self, root, callback=None):
        self.root = root
        self.root.title("Excel文件处理工具")
        self.callback = callback

        # 尝试加载配置
        self.config = self.load_config()

        # 文件路径存储 - 使用配置中的值或默认为空，添加"设计说明书"和"残值"
        default_file_paths = {
            "屏柜配线套管": "",
            "屏柜BOM表": "",
            "线材规格定义": "",
            "线长定义": "",
            "压头匹配": "",
            "套管匹配": "",
            "设计说明书": "",
            "BOM模板": "",
            "自备料库": "",
            "物料辅料与把手": "",
            "残值": ""
        }

        # 添加停止标志
        self.stop_processing = False
        self.file_paths = self.config.get("file_paths", {})
        # 确保所有默认字段都存在
        for key, value in default_file_paths.items():
            if key not in self.file_paths:
                self.file_paths[key] = value

        self.output_dir = self.config.get("output_dir", "")
        self.output_filename = tk.StringVar(value=self.config.get("output_filename", "output.xlsx"))
        
        # 批量处理目录变量
        self.batch_input_dir = self.config.get("batch_input_dir", "")
        self.batch_output_dir = self.config.get("batch_output_dir", "")

        # 项目工程类型变量 - 使用配置中的值或默认值
        self.project_type_var = tk.StringVar(value=self.config.get("project_type", "通用"))

        # 单选按钮变量 - 使用配置中的值或默认值
        self.material_var = tk.StringVar(value=self.config.get("material_type", "普通型"))
        self.cable_var = tk.StringVar(value=self.config.get("cable_type", "G"))

        # 界面模式变量
        self.interface_mode = self.config.get("interface_mode", "手动选择")

        # 创建主容器
        self.create_main_container()
        self.create_widgets()

        # 添加Windows鼠标滚轮绑定
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

        # 设置变量监听，当值改变时自动保存配置
        self.setup_auto_save_listeners()

        # 恢复窗口位置和大小
        self.restore_window_geometry()

    def load_config(self):
        """加载配置文件"""
        try:
            if self.CONFIG_FILE.exists():
                with open(self.CONFIG_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            # 配置文件损坏或格式错误时忽略
            pass
        return {}

    def save_config(self):
        """保存配置到文件"""
        # 获取当前窗口几何信息
        geometry = self.root.winfo_geometry() if self.root.winfo_exists() else "600x600+100+100"
        
        config = {
            "file_paths": self.file_paths,
            "output_dir": self.output_dir,
            "output_filename": self.output_filename.get(),
            "material_type": self.material_var.get(),
            "cable_type": self.cable_var.get(),
            "project_type": self.project_type_var.get(),
            "interface_mode": self.interface_mode,
            "window_geometry": geometry,
            "batch_input_dir": getattr(self, 'batch_input_dir', ''),
            "batch_output_dir": getattr(self, 'batch_output_dir', '')
        }
        
        # 保存折叠状态
        for mode in ["manual", "auto", "batch"]:
            collapse_var_name = f"config_collapsed_{mode}"
            if hasattr(self, collapse_var_name):
                collapse_var = getattr(self, collapse_var_name)
                config[f"config_collapsed_{mode}"] = collapse_var.get()

        try:
            with open(self.CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            # print(f"保存配置失败: {e}")
            pass

    def on_close(self):
        """窗口关闭时保存配置"""
        self.save_config()
        self.root.destroy()

    def setup_auto_save_listeners(self):
        """设置自动保存监听器"""
        # 监听单选按钮变量的变化
        self.project_type_var.trace('w', self.on_config_changed)
        self.material_var.trace('w', self.on_config_changed)
        self.cable_var.trace('w', self.on_config_changed)
        self.output_filename.trace('w', self.on_config_changed)
        
        # 添加批量处理输入框监听器（需要等待GUI创建完成）
        def setup_batch_listeners():
            if hasattr(self, 'batch_input_dir_entry'):
                self.batch_input_dir_entry.bind('<KeyRelease>', self.on_batch_input_changed)
                self.batch_input_dir_entry.bind('<FocusOut>', self.on_batch_input_changed)
            if hasattr(self, 'batch_output_dir_entry'):
                self.batch_output_dir_entry.bind('<KeyRelease>', self.on_batch_output_changed)
                self.batch_output_dir_entry.bind('<FocusOut>', self.on_batch_output_changed)
        
        # 延迟绑定，等待GUI创建完成
        self.root.after(100, setup_batch_listeners)

    def on_batch_input_changed(self, event=None):
        """批量处理输入目录改变时的回调"""
        self.batch_input_dir = self.batch_input_dir_entry.get()
        self.on_config_changed()
        # 更新状态显示
        if self.interface_mode == "批量处理":
            has_input_dir = bool(self.batch_input_dir)
            has_output_dir = bool(self.batch_output_dir)
            if has_input_dir and has_output_dir:
                self.status_var.set("批量处理模式 - 输入和输出目录已配置，可以开始处理")
            elif has_input_dir:
                self.status_var.set("批量处理模式 - 输入目录已配置，请选择输出目录")
            else:
                self.status_var.set("批量处理模式 - 请选择输入和输出目录")

    def on_batch_output_changed(self, event=None):
        """批量处理输出目录改变时的回调"""
        self.batch_output_dir = self.batch_output_dir_entry.get()
        self.on_config_changed()
        # 更新状态显示
        if self.interface_mode == "批量处理":
            has_input_dir = bool(self.batch_input_dir)
            has_output_dir = bool(self.batch_output_dir)
            if has_input_dir and has_output_dir:
                self.status_var.set("批量处理模式 - 输入和输出目录已配置，可以开始处理")
            elif has_output_dir:
                self.status_var.set("批量处理模式 - 输出目录已配置，请选择输入目录")
            else:
                self.status_var.set("批量处理模式 - 请选择输入和输出目录")

    def on_config_changed(self, *args):
        """配置改变时的回调函数"""
        # 延迟保存，避免频繁写入
        if hasattr(self, '_save_timer'):
            self.root.after_cancel(self._save_timer)
        self._save_timer = self.root.after(500, self.delayed_save_config)  # 500ms后保存

    def delayed_save_config(self):
        """延迟保存配置，并显示状态"""
        self.save_config()
        # 临时显示保存状态
        old_status = self.status_var.get()
        self.status_var.set("配置已自动保存")
        self.root.after(2000, lambda: self.status_var.set(old_status))  # 2秒后恢复原状态

    def restore_window_geometry(self):
        """恢复窗口位置和大小"""
        geometry = self.config.get("window_geometry", "600x600+100+100")
        try:
            self.root.geometry(geometry)
        except tk.TclError:
            # 如果几何信息无效，使用默认值
            self.root.geometry("600x600")
            self.root.minsize(550, 600)

    def show_initialization_status(self):
        """显示初始化状态"""
        if self.CONFIG_FILE.exists():
            # 根据当前界面模式统计已配置的文件数量
            current_mode = self.interface_mode
            if current_mode == "手动选择":
                required_files = ["屏柜配线套管", "屏柜BOM表", "线材规格定义", "线长定义", "压头匹配", "套管匹配", "BOM模板", "自备料库"]
                configured_files = sum(1 for file_type in required_files if self.file_paths.get(file_type))
                total_files = len(required_files)
                if configured_files > 0:
                    self.status_var.set(f"已从上次会话恢复配置 - {current_mode}模式: {configured_files}/{total_files}个文件已配置")
                else:
                    self.status_var.set(f"配置文件已加载 - {current_mode}模式，请选择输入文件")
            elif current_mode == "自动识别":
                required_files = ["屏柜配线套管", "屏柜BOM表", "线材规格定义", "线长定义", "压头匹配", "套管匹配", "设计说明书", "BOM模板", "自备料库"]
                configured_files = sum(1 for file_type in required_files if self.file_paths.get(file_type))
                total_files = len(required_files)
                if configured_files > 0:
                    self.status_var.set(f"已从上次会话恢复配置 - {current_mode}模式: {configured_files}/{total_files}个文件已配置")
                else:
                    self.status_var.set(f"配置文件已加载 - {current_mode}模式，请选择输入文件")
            elif current_mode == "批量处理":
                # 检查批量处理配置
                has_input_dir = bool(self.batch_input_dir)
                has_output_dir = bool(self.batch_output_dir)
                if has_input_dir and has_output_dir:
                    self.status_var.set(f"已从上次会话恢复配置 - {current_mode}模式: 输入和输出目录已配置")
                elif has_input_dir or has_output_dir:
                    self.status_var.set(f"已从上次会话恢复配置 - {current_mode}模式: 部分目录已配置")
                else:
                    self.status_var.set(f"配置文件已加载 - {current_mode}模式，请选择输入和输出目录")
        else:
            self.status_var.set(f"欢迎使用Excel文件处理工具 - {self.interface_mode}模式")

    def create_collapsible_config_section(self, parent, mode):
        """创建可折叠的配置文件区域"""
        # 配置文件区域主框架
        config_main_frame = ttk.Frame(parent)
        config_main_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 配置文件标题栏（包含折叠按钮）
        config_header = ttk.Frame(config_main_frame)
        config_header.pack(fill=tk.X)
        
        # 折叠状态变量
        collapse_var_name = f"config_collapsed_{mode}"
        if not hasattr(self, collapse_var_name):
            setattr(self, collapse_var_name, tk.BooleanVar(value=self.config.get(f"config_collapsed_{mode}", True)))
        
        collapse_var = getattr(self, collapse_var_name)
        
        # 折叠按钮
        def toggle_collapse():
            is_collapsed = collapse_var.get()
            collapse_var.set(not is_collapsed)
            if is_collapsed:
                config_content.pack(fill=tk.X, pady=(5, 0))
                toggle_btn.config(text="▼")
            else:
                config_content.pack_forget()
                toggle_btn.config(text="▲")
            # 保存折叠状态
            self.on_config_changed()
        
        toggle_btn = ttk.Button(
            config_header, 
            text="▲" if collapse_var.get() else "▼",
            width=3,
            command=toggle_collapse
        )
        toggle_btn.pack(side=tk.LEFT)
        
        # 配置文件标签
        ttk.Label(
            config_header, 
            text="配置文件",
            font=("Arial", 9, "bold"),
            foreground="#21618C"
        ).pack(side=tk.LEFT, padx=(5, 0))
        
        # 配置文件内容区域
        config_content = ttk.Frame(config_main_frame)
        if not collapse_var.get():
            config_content.pack(fill=tk.X, pady=(5, 0))
        
        # 配置文件列表
        config_files = ["线材规格定义", "线长定义", "压头匹配", "套管匹配", "BOM模板", "自备料库", "物料辅料与把手", "残值"]
        for file_type in config_files:
            self.create_file_row(config_content, file_type, mode)

    def create_main_container(self):
        """创建带滚动条的主容器"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # 创建画布和滚动条
        self.canvas = Canvas(self.main_frame, highlightthickness=0)
        self.scrollbar = Scrollbar(self.main_frame, orient=VERTICAL, command=self.canvas.yview)

        # 布局
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 配置画布
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        self.canvas.bind("<Configure>", self.on_canvas_configure)

        # 创建内容框架
        self.content_frame = ttk.Frame(self.canvas)
        self.canvas_frame = self.canvas.create_window((0, 0), window=self.content_frame, anchor="nw")

        # 更新滚动区域
        self.content_frame.bind("<Configure>", self.on_content_configure)

    def on_content_configure(self, event=None):
        """当内容改变时更新滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def on_canvas_configure(self, event):
        """调整画布配置"""
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_frame, width=canvas_width)

    def on_mousewheel(self, event):
        """Windows鼠标滚轮处理"""
        self.canvas.yview_scroll(-event.delta // 120, "units")

    def create_widgets(self):
        """创建界面控件"""
        # 先创建状态栏，确保 status_var 已初始化
        self.create_status_bar()
        # 设置内边距
        self.content_frame.configure(padding=(20, 10, 20, 10))

        # 标题
        ttk.Label(
            self.content_frame,
            text="Excel文件处理工具",
            font=("Arial", 14, "bold"),
            padding=(0, 0, 0, 15)
        ).pack(fill=tk.X, pady=(0, 10))

        # 创建Notebook用于切换界面
        self.notebook = ttk.Notebook(self.content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建三个标签页
        self.manual_frame = ttk.Frame(self.notebook)
        self.auto_frame = ttk.Frame(self.notebook)
        self.batch_frame = ttk.Frame(self.notebook)

        self.notebook.add(self.manual_frame, text="手动选择")
        self.notebook.add(self.auto_frame, text="自动识别")
        self.notebook.add(self.batch_frame, text="批量处理")

        # 根据配置选择初始标签页
        if self.interface_mode == "自动识别":
            self.notebook.select(1)
        else:
            self.notebook.select(0)

        # 绑定标签页切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

        # 创建手动选择界面
        self.create_manual_interface()

        # 创建自动识别界面
        self.create_auto_interface()
        
        # 创建批量处理界面
        self.create_batch_interface()

        # 输出设置区域（共用）- 保存为实例变量
        self.output_frame = ttk.LabelFrame(self.content_frame, text="输出设置")
        self.output_frame.pack(fill=tk.X, padx=5, pady=10)

        # 输出路径
        self.create_output_row(self.output_frame, "输出路径", self.select_output_dir, "浏览...")

        # 说明信息
        info_row = ttk.Frame(self.output_frame)
        info_row.pack(fill=tk.X, padx=5, pady=3)
        ttk.Label(
            info_row, 
            text="• BOM清单将自动按项目编号-位置命名并保存到输出路径\n• 点击生成输出表可生成包含导线统计等信息的output表",
            foreground="#2E86C1",
            justify=tk.LEFT,
            wraplength=500
        ).pack(side=tk.LEFT, padx=(0, 5))

        # 按钮区域
        button_row = ttk.Frame(self.output_frame)
        button_row.pack(fill=tk.X, padx=5, pady=5)
        
        # 生成输出表按钮
        self.output_table_button = ttk.Button(
            button_row,
            text="生成输出表",
            width=12,
            command=self.generate_output_table
        )
        self.output_table_button.pack(side=tk.LEFT, padx=(0, 10))
        # 不再初始禁用，用户可以随时使用
        
        ttk.Label(
            button_row,
            text="生成导线统计、并线统计等信息的output表",
            foreground="#7D7D7D"
        ).pack(side=tk.LEFT)

        # 开始处理按钮 - 保存为实例变量
        self.process_button_frame = ttk.Frame(self.content_frame)
        self.process_button_frame.pack(fill=tk.X, padx=5, pady=(15, 10))

        # 创建按钮容器
        button_container = ttk.Frame(self.process_button_frame)
        button_container.pack()

        # 确保按钮存在且位置正确
        self.process_button = ttk.Button(
            button_container,
            text="开始处理",
            width=15,
            style="Accent.TButton",
            command=self.confirm_selection
        )
        self.process_button.pack(side=tk.LEFT, padx=(0, 5))

        # 终止按钮
        self.stop_button = ttk.Button(
            button_container,
            text="终止",
            width=10,
            command=self.stop_processing_action,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=(5, 0))

        # 设置样式
        self.set_styles()

        # 设置最小窗口尺寸（实际尺寸由restore_window_geometry设置）
        self.root.minsize(550, 600)

        # 初始化输入框的值
        self.initialize_entry_values()

        # 显示配置恢复状态
        self.show_initialization_status()

        # 初始化时根据当前选项卡设置组件可见性
        self.on_tab_changed()

    def create_manual_interface(self):
        """创建手动选择界面"""
        # 配置文件区域（可折叠）
        self.create_collapsible_config_section(self.manual_frame, "manual")
        
        # 用户输入文件区域
        user_input_frame = ttk.LabelFrame(self.manual_frame, text="用户输入文件")
        user_input_frame.pack(fill=tk.X, padx=5, pady=5)

        # 用户输入文件（手动选择模式不需要设计说明书）
        user_files = ["屏柜配线套管", "屏柜BOM表"]
        for file_type in user_files:
            self.create_file_row(user_input_frame, file_type, "manual")

        # 项目工程类型选择区域
        project_frame = ttk.LabelFrame(self.manual_frame, text="项目工程类型")
        project_frame.pack(fill=tk.X, padx=5, pady=5)

        # 项目工程类型单选按钮组
        project_types = [("通用", "通用"), ("福建", "福建")]
        for text, value in project_types:
            ttk.Radiobutton(
                project_frame,
                text=text,
                variable=self.project_type_var,
                value=value
            ).pack(side=tk.LEFT, padx=10, pady=5)

        # 线材规格选择区域
        material_frame = ttk.LabelFrame(self.manual_frame, text="线材规格")
        material_frame.pack(fill=tk.X, padx=5, pady=5)

        # 材料类型单选按钮组
        material_types = [("普通型", "普通型"), ("交联聚乙烯", "交联聚乙烯"), ("自定义线材", "自定义线材")]
        for text, value in material_types:
            ttk.Radiobutton(
                material_frame,
                text=text,
                variable=self.material_var,
                value=value
            ).pack(side=tk.LEFT, padx=10, pady=5)

        # 线径选型选择区域
        cable_frame = ttk.LabelFrame(self.manual_frame, text="线径选型")
        cable_frame.pack(fill=tk.X, padx=5, pady=5)

        # 线缆类型单选按钮组
        cable_types = [("G", "G"), ("X", "X"), ("N", "N"), ("Z", "Z")]
        for text, value in cable_types:
            ttk.Radiobutton(
                cable_frame,
                text=text,
                variable=self.cable_var,
                value=value
            ).pack(side=tk.LEFT, padx=10, pady=5)

        # 进度显示区域
        progress_frame = ttk.LabelFrame(self.manual_frame, text="处理进度")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 进度条
        self.manual_progress_var = tk.DoubleVar()
        self.manual_progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.manual_progress_var,
            maximum=100,
            length=400
        )
        self.manual_progress_bar.pack(fill=tk.X, padx=10, pady=5)
        
        # 进度文本
        self.manual_progress_text = tk.StringVar(value="准备就绪")
        ttk.Label(
            progress_frame,
            textvariable=self.manual_progress_text,
            foreground="#2E86C1"
        ).pack(padx=10, pady=5)
        
        # 处理结果显示
        self.manual_results_text = tk.Text(
            self.manual_frame,
            height=6,
            width=70,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.manual_results_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        manual_scrollbar = ttk.Scrollbar(self.manual_frame, orient=tk.VERTICAL, command=self.manual_results_text.yview)
        manual_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.manual_results_text.config(yscrollcommand=manual_scrollbar.set)

    def create_auto_interface(self):
        """创建自动识别界面"""
        # 配置文件区域（可折叠）
        self.create_collapsible_config_section(self.auto_frame, "auto")
        
        # 用户输入文件区域
        user_input_frame = ttk.LabelFrame(self.auto_frame, text="用户输入文件")
        user_input_frame.pack(fill=tk.X, padx=5, pady=5)

        # 用户输入文件（自动识别模式包含设计说明书）
        user_files = ["屏柜配线套管", "屏柜BOM表", "设计说明书"]
        for file_type in user_files:
            self.create_file_row(user_input_frame, file_type, "auto")

        # 项目工程类型选择区域（保留）
        project_frame = ttk.LabelFrame(self.auto_frame, text="项目工程类型")
        project_frame.pack(fill=tk.X, padx=5, pady=5)

        # 项目工程类型单选按钮组
        project_types = [("通用", "通用"), ("福建", "福建")]
        for text, value in project_types:
            ttk.Radiobutton(
                project_frame,
                text=text,
                variable=self.project_type_var,
                value=value
            ).pack(side=tk.LEFT, padx=10, pady=5)

        # 提示信息
        info_frame = ttk.LabelFrame(self.auto_frame, text="说明")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(
            info_frame,
            text="在自动识别模式下，需要提供设计说明书，系统将自动从中识别线材规格和线径选型",
            wraplength=500,
            justify=tk.LEFT,
            foreground="#2E86C1"
        ).pack(padx=10, pady=5)
        
        # 测试功能区域
        test_frame = ttk.LabelFrame(self.auto_frame, text="测试功能")
        test_frame.pack(fill=tk.X, padx=5, pady=5)
        
        test_btn_frame = ttk.Frame(test_frame)
        test_btn_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            test_btn_frame,
            text="测试设计说明书识别",
            command=self.test_design_doc_recognition
        ).pack(side=tk.LEFT)
        
        ttk.Label(
            test_btn_frame,
            text="仅测试识别功能，不进行数据处理",
            foreground="#7D7D7D"
        ).pack(side=tk.LEFT, padx=(10, 0))

        # 进度显示区域
        progress_frame = ttk.LabelFrame(self.auto_frame, text="处理进度")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 进度条
        self.auto_progress_var = tk.DoubleVar()
        self.auto_progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.auto_progress_var,
            maximum=100,
            length=400
        )
        self.auto_progress_bar.pack(fill=tk.X, padx=10, pady=5)
        
        # 进度文本
        self.auto_progress_text = tk.StringVar(value="准备就绪")
        ttk.Label(
            progress_frame,
            textvariable=self.auto_progress_text,
            foreground="#2E86C1"
        ).pack(padx=10, pady=5)
        
        # 处理结果显示
        self.auto_results_text = tk.Text(
            self.auto_frame,
            height=6,
            width=70,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.auto_results_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        auto_scrollbar = ttk.Scrollbar(self.auto_frame, orient=tk.VERTICAL, command=self.auto_results_text.yview)
        auto_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.auto_results_text.config(yscrollcommand=auto_scrollbar.set)

    def create_batch_interface(self):
        """创建批量处理界面"""
        # 配置文件区域（可折叠）
        self.create_collapsible_config_section(self.batch_frame, "batch")
        
        # 批量处理设置区域
        batch_settings_frame = ttk.LabelFrame(self.batch_frame, text="批量处理设置")
        batch_settings_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 输入目录选择
        input_dir_frame = ttk.Frame(batch_settings_frame)
        input_dir_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(input_dir_frame, text="输入目录:", width=12).pack(side=tk.LEFT)
        
        # 先放置按钮
        ttk.Button(
            input_dir_frame,
            text="浏览...",
            width=8,
            command=self.select_batch_input_dir
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 再放置输入框，留出按钮的空间
        self.batch_input_dir_entry = ttk.Entry(input_dir_frame)
        self.batch_input_dir_entry.pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)
        
        # 输出目录选择
        output_dir_frame = ttk.Frame(batch_settings_frame)
        output_dir_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(output_dir_frame, text="输出目录:", width=12).pack(side=tk.LEFT)
        
        # 先放置按钮
        ttk.Button(
            output_dir_frame,
            text="浏览...",
            width=8,
            command=self.select_batch_output_dir
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 再放置输入框，留出按钮的空间
        self.batch_output_dir_entry = ttk.Entry(output_dir_frame)
        self.batch_output_dir_entry.pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)
        
        # 说明信息
        info_frame = ttk.LabelFrame(self.batch_frame, text="说明")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        info_text = """批量处理功能说明：
• 自动扫描输入目录中的所有项目文件夹
• 每个项目需要包含：设计说明书、导线连接套管.xlsx、屏柜BOM表.xlsm
• 自动识别项目参数，无需手动配置
• 为每个项目生成独立的输出文件
• 生成综合汇总报告"""
        
        ttk.Label(
            info_frame,
            text=info_text,
            wraplength=500,
            justify=tk.LEFT,
            foreground="#2E86C1"
        ).pack(padx=10, pady=5)
        
        # 进度显示区域
        progress_frame = ttk.LabelFrame(self.batch_frame, text="处理进度")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 进度条
        self.batch_progress_var = tk.DoubleVar()
        self.batch_progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.batch_progress_var,
            maximum=100,
            length=400
        )
        self.batch_progress_bar.pack(fill=tk.X, padx=10, pady=5)
        
        # 进度文本
        self.batch_progress_text = tk.StringVar(value="准备就绪")
        ttk.Label(
            progress_frame,
            textvariable=self.batch_progress_text,
            foreground="#2E86C1"
        ).pack(padx=10, pady=5)
        
        # 批量处理按钮
        batch_button_frame = ttk.Frame(self.batch_frame)
        batch_button_frame.pack(fill=tk.X, padx=5, pady=10)

        self.batch_process_button = ttk.Button(
            batch_button_frame,
            text="开始批量处理",
            width=15,
            command=self.start_batch_processing
        )
        self.batch_process_button.pack(side=tk.LEFT, padx=5)

        # 批量处理终止按钮
        self.batch_stop_button = ttk.Button(
            batch_button_frame,
            text="终止",
            width=10,
            command=self.stop_batch_processing,
            state=tk.DISABLED
        )
        self.batch_stop_button.pack(side=tk.LEFT, padx=5)
        
        # 扫描项目按钮
        ttk.Button(
            batch_button_frame,
            text="扫描项目",
            width=12,
            command=self.scan_batch_projects
        ).pack(side=tk.LEFT, padx=5)
        
        # 批量处理结果显示
        self.batch_results_text = tk.Text(
            self.batch_frame,
            height=8,
            width=70,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.batch_results_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.batch_frame, orient=tk.VERTICAL, command=self.batch_results_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.batch_results_text.config(yscrollcommand=scrollbar.set)
        
        # 初始化批量处理输入框的值
        self.initialize_batch_values()

    def initialize_batch_values(self):
        """初始化批量处理输入框的值"""
        # 初始化批量处理输入框
        if hasattr(self, 'batch_input_dir_entry') and self.batch_input_dir:
            self.batch_input_dir_entry.delete(0, tk.END)
            self.batch_input_dir_entry.insert(0, self.batch_input_dir)
            
        if hasattr(self, 'batch_output_dir_entry') and self.batch_output_dir:
            self.batch_output_dir_entry.delete(0, tk.END)
            self.batch_output_dir_entry.insert(0, self.batch_output_dir)
        
        # 自动填充批量处理模式下的配置文件输入框
        batch_files = ["线材规格定义", "线长定义", "压头匹配", "套管匹配", "BOM模板", "自备料库", "物料辅料与把手", "残值"]
        for file_type in batch_files:
            file_path = self.file_paths.get(file_type, "")
            entry_widget = getattr(self, f"entry_batch_{file_type}", None)
            if entry_widget and file_path:
                entry_widget.delete(0, tk.END)
                entry_widget.insert(0, file_path)
        
        # 更新状态显示
        if self.interface_mode == "批量处理":
            has_input_dir = bool(self.batch_input_dir)
            has_output_dir = bool(self.batch_output_dir)
            if has_input_dir and has_output_dir:
                self.status_var.set("批量处理模式 - 输入和输出目录已配置，可以开始处理")
            elif has_input_dir or has_output_dir:
                self.status_var.set("批量处理模式 - 部分目录已配置，请完善配置")
            else:
                self.status_var.set("批量处理模式 - 请选择输入和输出目录")

    def on_tab_changed(self, event=None):
        """标签页切换事件处理"""
        current_tab = self.notebook.tab(self.notebook.select(), "text")
        self.interface_mode = current_tab
        
        # 根据当前选项卡设置组件可见性
        if current_tab == "批量处理":
            # 隐藏输出设置区域和开始处理按钮
            self.output_frame.pack_forget()
            self.process_button_frame.pack_forget()
            # 检查批量处理配置状态
            has_input_dir = bool(self.batch_input_dir)
            has_output_dir = bool(self.batch_output_dir)
            if has_input_dir and has_output_dir:
                self.status_var.set("批量处理模式 - 输入和输出目录已配置，可以开始处理")
            elif has_input_dir or has_output_dir:
                self.status_var.set("批量处理模式 - 部分目录已配置，请完善配置")
            else:
                self.status_var.set("批量处理模式 - 请选择输入和输出目录")
        else:
            # 显示输出设置区域和开始处理按钮
            self.output_frame.pack(fill=tk.X, padx=5, pady=10)
            self.process_button_frame.pack(fill=tk.X, padx=5, pady=(15, 10))
            self.status_var.set(f"切换到 {current_tab} 模式")
        
        # 触发配置变化处理
        self.on_config_changed()

    def initialize_entry_values(self):
        """初始化输入框的值"""
        # 定义每个模式支持的文件类型
        manual_files = ["屏柜配线套管", "屏柜BOM表", "线材规格定义", "线长定义", "压头匹配", "套管匹配", "BOM模板", "自备料库", "物料辅料与把手", "残值"]
        auto_files = ["屏柜配线套管", "屏柜BOM表", "线材规格定义", "线长定义", "压头匹配", "套管匹配", "设计说明书", "BOM模板", "自备料库", "物料辅料与把手", "残值"]
        
        # 初始化文件路径输入框
        for file_type in self.file_paths:
            file_path = self.file_paths[file_type]
            if not file_path:
                continue
                
            # 手动模式的输入框（不包括设计说明书）
            if file_type in manual_files:
                entry_widget_manual = getattr(self, f"entry_manual_{file_type}", None)
                if entry_widget_manual:
                    entry_widget_manual.delete(0, tk.END)
                    entry_widget_manual.insert(0, file_path)
            
            # 自动模式的输入框（包括设计说明书）
            if file_type in auto_files:
                entry_widget_auto = getattr(self, f"entry_auto_{file_type}", None)
                if entry_widget_auto:
                    entry_widget_auto.delete(0, tk.END)
                    entry_widget_auto.insert(0, file_path)

        # 初始化输出路径输入框
        if self.output_dir:
            self.output_dir_entry.delete(0, tk.END)
            self.output_dir_entry.insert(0, self.output_dir)

    def create_file_row(self, parent, file_type, mode):
        """创建单个文件选择行"""
        row = ttk.Frame(parent)
        row.pack(fill=tk.X, padx=5, pady=3)

        # 标签
        label = ttk.Label(row, text=f"{file_type}:", width=15, anchor=tk.W)
        label.pack(side=tk.LEFT, padx=(0, 5))

        # 输入框
        entry = ttk.Entry(row)
        entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 选择按钮
        btn = ttk.Button(row, text="选择", width=6,
                         command=lambda f=file_type, e=entry: self.select_file(f, e))
        btn.pack(side=tk.RIGHT, padx=(5, 0))

        setattr(self, f"entry_{mode}_{file_type}", entry)

    def create_output_row(self, parent, label_text, command, button_text):
        """创建输出路径选择行"""
        row = ttk.Frame(parent)
        row.pack(fill=tk.X, padx=5, pady=3)

        ttk.Label(row, text=label_text, width=15, anchor=tk.W).pack(side=tk.LEFT, padx=(0, 5))
        self.output_dir_entry = ttk.Entry(row)
        self.output_dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(row, text=button_text, width=6, command=command).pack(side=tk.RIGHT)



    def create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar(value="请选择输入文件和输出位置")
        status_bar = ttk.Label(
            self.root,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.CENTER,
            padding=(5, 3, 5, 3)
        )
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=0, pady=0)

    def set_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.configure("Accent.TButton", font=("Arial", 10, "bold"), foreground="#2E86C1")
        style.configure("TLabelframe", padding=(8, 5, 8, 10))
        style.configure("TLabelframe.Label", font=("Arial", 9, "bold"), foreground="#21618C")
        style.configure("TButton", padding=(3, 3, 3, 3))
        style.configure("TLabel", padding=3)
        style.configure("TEntry", padding=3)

    def select_file(self, file_type, entry_widget):
        """选择文件处理"""
        file_path = filedialog.askopenfilename(
            title=f"选择{file_type}文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )

        if file_path:
            self.file_paths[file_type] = file_path
            entry_widget.delete(0, tk.END)
            entry_widget.insert(0, file_path)
            
            # 同步更新另一个界面的相同输入框（只有两个模式都有的文件才同步）
            if file_type != "设计说明书":  # 设计说明书只在自动识别模式存在
                current_tab = self.notebook.tab(self.notebook.select(), "text")
                other_mode = "auto" if current_tab == "手动选择" else "manual"
                other_entry = getattr(self, f"entry_{other_mode}_{file_type}", None)
                if other_entry:
                    other_entry.delete(0, tk.END)
                    other_entry.insert(0, file_path)
            
            self.status_var.set(f"已选择: {file_type}文件")
            # 立即保存配置
            self.save_config()

    def select_output_dir(self):
        """选择输出目录"""
        dir_path = filedialog.askdirectory(title="选择输出目录")

        if dir_path:
            self.output_dir = dir_path
            self.output_dir_entry.delete(0, tk.END)
            self.output_dir_entry.insert(0, dir_path)
            self.status_var.set(f"输出目录: {dir_path}")
            # 立即保存配置
            self.save_config()

    def generate_output_table(self):
        """生成输出表（可选功能）"""
        # 获取当前界面模式
        current_tab = self.notebook.tab(self.notebook.select(), "text")
        
        # 根据界面模式检查必要的文件
        if current_tab == "手动选择":
            required_files = ["屏柜配线套管", "线材规格定义", "线长定义", "压头匹配", "套管匹配"]
        else:
            required_files = ["屏柜配线套管", "线材规格定义", "线长定义", "压头匹配", "套管匹配", "设计说明书"]
        
        # 检查输入文件
        missing = [name for name in required_files if not self.file_paths.get(name)]
        if missing:
            self.status_var.set(f"请先选择以下文件：{'、'.join(missing)}")
            self.root.bell()
            return

        # 检查输出目录
        if not self.output_dir:
            self.status_var.set("请先选择输出目录")
            self.root.bell()
            return

        # 生成版本化的输出文件名
        from main import get_versioned_filename
        base_output_path = os.path.join(self.output_dir, "output.xlsx")
        output_path = get_versioned_filename(base_output_path)
        
        # 清空结果显示
        if current_tab == "手动选择":
            self.manual_results_text.config(state=tk.NORMAL)
            self.manual_results_text.delete(1.0, tk.END)
            self.manual_results_text.config(state=tk.DISABLED)
            self.manual_progress_var.set(0)
            self.update_manual_status("正在生成输出表...")
            self.append_manual_results("开始生成输出表...")
        else:
            self.auto_results_text.config(state=tk.NORMAL)
            self.auto_results_text.delete(1.0, tk.END)
            self.auto_results_text.config(state=tk.DISABLED)
            self.auto_progress_var.set(0)
            self.update_auto_status("正在生成输出表...")
            self.append_auto_results("开始生成输出表...")
        
        # 创建进度回调函数
        def progress_callback(message, current=None, total=None):
            if current_tab == "手动选择":
                if total and total > 0:
                    progress = (current / total) * 100
                    self.manual_progress_var.set(progress)
                self.update_manual_status(message)
                self.append_manual_results(message)
            else:
                if total and total > 0:
                    progress = (current / total) * 100
                    self.auto_progress_var.set(progress)
                self.update_auto_status(message)
                self.append_auto_results(message)
        
        # 调用处理函数，但只生成输出表，不生成BOM
        if self.callback:
            try:
                # 调用处理函数，传递生成模式参数
                if current_tab == "手动选择":
                    self.callback(
                        self.file_paths,
                        output_path,  # 传递具体文件路径，不是目录
                        material_type=self.material_var.get(),
                        cable_type=self.cable_var.get(),
                        project_type=self.project_type_var.get(),
                        interface_mode=current_tab,
                        generate_output_only=True,  # 新增参数，表示只生成输出表
                        progress_callback=progress_callback,  # 添加进度回调
                        save_residual_table=True  # 单项目处理时保存残值表
                    )
                else:
                    self.callback(
                        self.file_paths,
                        output_path,  # 传递具体文件路径，不是目录
                        material_type="自动识别",
                        cable_type="自动识别",
                        project_type=self.project_type_var.get(),
                        interface_mode=current_tab,
                        generate_output_only=True,  # 新增参数，表示只生成输出表
                        progress_callback=progress_callback,  # 添加进度回调
                        save_residual_table=True  # 单项目处理时保存残值表
                    )
                
                # 处理完成
                if current_tab == "手动选择":
                    self.manual_progress_var.set(100)
                    self.update_manual_status("输出表生成完成")
                    self.append_manual_results("✓ 输出表生成完成！")
                    self.append_manual_results(f"输出文件: {output_path}")
                else:
                    self.auto_progress_var.set(100)
                    self.update_auto_status("输出表生成完成")
                    self.append_auto_results("✓ 输出表生成完成！")
                    self.append_auto_results(f"输出文件: {output_path}")
                    
            except Exception as e:
                # 错误处理
                error_msg = f"生成输出表失败: {str(e)}"
                if current_tab == "手动选择":
                    self.update_manual_status("生成失败")
                    self.append_manual_results(f"✗ {error_msg}")
                else:
                    self.update_auto_status("生成失败")
                    self.append_auto_results(f"✗ {error_msg}")
                
                # 显示错误对话框
                messagebox.showerror("生成错误", error_msg)

    def test_design_doc_recognition(self):
        """测试设计说明书识别功能"""
        design_doc_path = self.file_paths.get("设计说明书")
        
        if not design_doc_path:
            messagebox.showwarning("提示", "请先选择设计说明书文件")
            return
        
        if not os.path.exists(design_doc_path):
            messagebox.showerror("错误", "设计说明书文件不存在，请重新选择")
            return
        
        # 清空结果显示
        self.auto_results_text.config(state=tk.NORMAL)
        self.auto_results_text.delete(1.0, tk.END)
        self.auto_results_text.config(state=tk.DISABLED)
        self.auto_progress_var.set(0)
        
        # 显示测试开始状态
        self.update_auto_status("正在测试设计说明书识别功能...")
        self.append_auto_results("开始测试设计说明书识别功能...")
        self.append_auto_results(f"测试文件: {design_doc_path}")
        
        try:
            # 创建处理器并测试
            processor = DesignDocProcessor()
            material_spec, cable_type, is_rotating_cabinet, small_busbar_data, success, error_msg = processor.process_design_document(design_doc_path)
            
            if success:
                # 成功识别
                cabinet_info = ""
                if processor.cabinet_type:
                    if is_rotating_cabinet:
                        cabinet_info = f"柜式要求: {processor.cabinet_type} (旋转柜，长度×2)\n"
                    else:
                        cabinet_info = f"柜式要求: {processor.cabinet_type} (普通柜体)\n"
                
                # 构建小母线信息
                small_busbar_info = ""
                if small_busbar_data:
                    bracket_info = small_busbar_data.get('bracket', {})
                    copper_info = small_busbar_data.get('copper', {})
                    
                    if bracket_info.get('layout'):
                        small_busbar_info += f"小母线支架: {bracket_info.get('layout')} ({bracket_info.get('quantity', '未知数量')})\n"
                    
                    if copper_info.get('spec'):
                        small_busbar_info += f"小母线铜棒: {copper_info.get('spec')} ({copper_info.get('quantity', '未知数量')})\n"
                    
                    if small_busbar_info:
                        small_busbar_info = f"{small_busbar_info}\n"
                
                # 更新进度和结果显示
                self.auto_progress_var.set(100)
                self.update_auto_status("测试完成 - 识别成功")
                self.append_auto_results("✓ 设计说明书识别成功！")
                self.append_auto_results(f"线材规格: {material_spec}")
                self.append_auto_results(f"线径选型: {cable_type}")
                if cabinet_info:
                    self.append_auto_results(f"柜式要求: {processor.cabinet_type}")
                if small_busbar_info:
                    self.append_auto_results("小母线信息:")
                    self.append_auto_results(small_busbar_info)
                self.append_auto_results("识别结果将在自动识别模式下自动应用。")
                
                result_msg = (
                    f"✓ 设计说明书识别成功！\n\n"
                    f"线材规格: {material_spec}\n"
                    f"线径选型: {cable_type}\n"
                    f"{cabinet_info}"
                    f"{small_busbar_info}"
                    f"识别结果将在自动识别模式下自动应用。"
                )
                messagebox.showinfo("识别成功", result_msg)
                
                status_text = f"测试完成 - 识别成功: {material_spec}, {cable_type}"
                if is_rotating_cabinet:
                    status_text += " (旋转柜)"
                self.status_var.set(status_text)
            else:
                # 识别失败
                self.auto_progress_var.set(100)
                self.update_auto_status("测试完成 - 识别失败")
                self.append_auto_results("✗ 设计说明书识别失败")
                self.append_auto_results(f"错误信息: {error_msg}")
                self.append_auto_results("建议检查文件格式或使用手动选择模式。")
                
                result_msg = (
                    f"✗ 设计说明书识别失败\n\n"
                    f"错误信息:\n{error_msg}\n\n"
                    f"建议检查文件格式或使用手动选择模式。"
                )
                messagebox.showwarning("识别失败", result_msg)
                self.status_var.set("测试完成 - 识别失败，请检查文件格式")
                
        except Exception as e:
            error_msg = f"测试过程中发生错误: {str(e)}"
            self.auto_progress_var.set(100)
            self.update_auto_status("测试失败")
            self.append_auto_results(f"✗ {error_msg}")
            messagebox.showerror("测试错误", error_msg)
            self.status_var.set("测试失败 - 发生错误")

    def stop_processing_action(self):
        """终止处理操作"""
        self.stop_processing = True
        self.status_var.set("正在终止处理...")

        # 获取当前界面模式
        current_tab = self.notebook.tab(self.notebook.select(), "text")
        if current_tab == "手动选择":
            self.update_manual_status("用户请求终止处理")
            self.append_manual_results("✗ 处理已被用户终止")
        else:
            self.update_auto_status("用户请求终止处理")
            self.append_auto_results("✗ 处理已被用户终止")

        # 重新启用开始按钮，禁用终止按钮
        self.process_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

    def stop_batch_processing(self):
        """终止批量处理操作"""
        self.stop_processing = True
        self.update_batch_status("正在终止批量处理...")
        self.append_batch_results("✗ 批量处理已被用户终止")

        # 重新启用开始按钮，禁用终止按钮
        self.batch_process_button.config(state=tk.NORMAL)
        self.batch_stop_button.config(state=tk.DISABLED)

    def confirm_selection(self):
        """确认并开始处理"""
        # 重置停止标志
        self.stop_processing = False

        # 获取当前界面模式
        current_tab = self.notebook.tab(self.notebook.select(), "text")
        
        # 根据界面模式检查必要的文件
        if current_tab == "手动选择":
            # 手动选择模式需要的文件（不包括设计说明书）
            required_files = ["屏柜配线套管", "屏柜BOM表", "线材规格定义", "线长定义", "压头匹配", "套管匹配", "BOM模板", "自备料库"]
        else:
            # 自动识别模式需要的文件（包括设计说明书）
            required_files = ["屏柜配线套管", "屏柜BOM表", "线材规格定义", "线长定义", "压头匹配", "套管匹配", "设计说明书", "BOM模板", "自备料库"]
        
        # 检查输入文件
        missing = [name for name in required_files if not self.file_paths.get(name)]
        if missing:
            self.status_var.set(f"请先选择以下文件：{'、'.join(missing)}")
            self.root.bell()
            return

        # 检查输出目录
        if not self.output_dir:
            self.status_var.set("请先选择输出目录")
            self.root.bell()
            return

        # 获取项目工程类型值
        project_type = self.project_type_var.get()

        # 清空结果显示
        if current_tab == "手动选择":
            self.manual_results_text.config(state=tk.NORMAL)
            self.manual_results_text.delete(1.0, tk.END)
            self.manual_results_text.config(state=tk.DISABLED)
            self.manual_progress_var.set(0)
        else:
            self.auto_results_text.config(state=tk.NORMAL)
            self.auto_results_text.delete(1.0, tk.END)
            self.auto_results_text.config(state=tk.DISABLED)
            self.auto_progress_var.set(0)

        if current_tab == "手动选择":
            # 手动选择模式：获取单选按钮的值
            material_type = self.material_var.get()
            cable_type = self.cable_var.get()
            self.update_manual_status(f"正在处理文件... 线材规格: {material_type}, 线径选型: {cable_type}, 项目工程类型: {project_type}")
            self.append_manual_results(f"开始处理 - 手动选择模式")
            self.append_manual_results(f"线材规格: {material_type}")
            self.append_manual_results(f"线径选型: {cable_type}")
            self.append_manual_results(f"项目工程类型: {project_type}")
        else:
            # 自动识别模式：设置为自动识别
            material_type = "自动识别"
            cable_type = "自动识别"
            self.update_auto_status(f"正在处理文件... 模式: 自动识别, 项目工程类型: {project_type}")
            self.append_auto_results(f"开始处理 - 自动识别模式")
            self.append_auto_results(f"项目工程类型: {project_type}")

        self.root.update()

        # 禁用开始按钮，启用停止按钮
        self.process_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        # 创建进度回调函数
        def progress_callback(message, current=None, total=None):
            # 检查是否需要终止处理
            if self.stop_processing:
                raise InterruptedError("用户请求终止处理")
            if current_tab == "手动选择":
                if total and total > 0:
                    progress = (current / total) * 100
                    self.manual_progress_var.set(progress)
                self.update_manual_status(message)
                self.append_manual_results(message)
            else:
                if total and total > 0:
                    progress = (current / total) * 100
                    self.auto_progress_var.set(progress)
                self.update_auto_status(message)
                self.append_auto_results(message)

        # 调用回调函数，传递输出目录而不是具体文件路径
        if self.callback:
            try:
                self.callback(
                    self.file_paths,
                    self.output_dir,  # 传递输出目录
                    material_type=material_type,
                    cable_type=cable_type,
                    project_type=project_type,
                    interface_mode=current_tab,
                    progress_callback=progress_callback,  # 添加进度回调
                    save_residual_table=True  # 单项目处理时保存残值表
                )
                
                # 处理完成
                if current_tab == "手动选择":
                    self.manual_progress_var.set(100)
                    self.update_manual_status("处理完成")
                    self.append_manual_results("✓ 处理完成！")
                else:
                    self.auto_progress_var.set(100)
                    self.update_auto_status("处理完成")
                    self.append_auto_results("✓ 处理完成！")
                    
            except InterruptedError as e:
                # 用户终止处理
                if current_tab == "手动选择":
                    self.update_manual_status("处理已终止")
                    self.append_manual_results("✗ 处理已被用户终止")
                else:
                    self.update_auto_status("处理已终止")
                    self.append_auto_results("✗ 处理已被用户终止")

            except Exception as e:
                # 错误处理
                error_msg = f"处理过程中发生错误: {str(e)}"
                if current_tab == "手动选择":
                    self.update_manual_status("处理失败")
                    self.append_manual_results(f"✗ {error_msg}")
                else:
                    self.update_auto_status("处理失败")
                    self.append_auto_results(f"✗ {error_msg}")

                # 显示错误对话框
                if not self.stop_processing:  # 只有在非终止状态下才显示错误对话框
                    messagebox.showerror("处理错误", error_msg)
            finally:
                # 恢复按钮状态
                self.process_button.config(state=tk.NORMAL)
                self.stop_button.config(state=tk.DISABLED)
                self.stop_processing = False

    def select_batch_input_dir(self):
        """选择批量处理输入目录"""
        from tkinter import filedialog
        dir_path = filedialog.askdirectory(title="选择批量处理输入目录")
        if dir_path:
            self.batch_input_dir = dir_path
            self.batch_input_dir_entry.delete(0, tk.END)
            self.batch_input_dir_entry.insert(0, dir_path)
            self.update_batch_status("已选择输入目录")
            # 立即保存配置
            self.save_config()
            # 更新状态栏显示
            self.status_var.set(f"已选择批量处理输入目录: {dir_path}")

    def select_batch_output_dir(self):
        """选择批量处理输出目录"""
        from tkinter import filedialog
        dir_path = filedialog.askdirectory(title="选择批量处理输出目录")
        if dir_path:
            self.batch_output_dir = dir_path
            self.batch_output_dir_entry.delete(0, tk.END)
            self.batch_output_dir_entry.insert(0, dir_path)
            self.update_batch_status("已选择输出目录")
            # 立即保存配置
            self.save_config()
            # 更新状态栏显示
            self.status_var.set(f"已选择批量处理输出目录: {dir_path}")

    def update_batch_status(self, message):
        """更新批量处理状态"""
        self.batch_progress_text.set(message)
        self.root.update()

    def append_batch_results(self, message):
        """追加批量处理结果"""
        self.batch_results_text.config(state=tk.NORMAL)
        self.batch_results_text.insert(tk.END, message + "\n")
        self.batch_results_text.see(tk.END)
        self.batch_results_text.config(state=tk.DISABLED)
        self.root.update()

    def update_manual_status(self, message):
        """更新手动选择模式状态"""
        self.manual_progress_text.set(message)
        self.root.update()

    def append_manual_results(self, message):
        """追加手动选择模式结果"""
        self.manual_results_text.config(state=tk.NORMAL)
        self.manual_results_text.insert(tk.END, message + "\n")
        self.manual_results_text.see(tk.END)
        self.manual_results_text.config(state=tk.DISABLED)
        self.root.update()

    def update_auto_status(self, message):
        """更新自动识别模式状态"""
        self.auto_progress_text.set(message)
        self.root.update()

    def append_auto_results(self, message):
        """追加自动识别模式结果"""
        self.auto_results_text.config(state=tk.NORMAL)
        self.auto_results_text.insert(tk.END, message + "\n")
        self.auto_results_text.see(tk.END)
        self.auto_results_text.config(state=tk.DISABLED)
        self.root.update()

    def scan_batch_projects(self):
        """扫描批量处理项目"""
        input_dir = self.batch_input_dir_entry.get().strip()
        if not input_dir:
            messagebox.showerror("错误", "请先选择输入目录")
            return

        try:
            from batch_processor import BatchProcessor
            config_dir = str(Path(__file__).parent / "input" / "配置文件")
            
            processor = BatchProcessor(input_dir, "", config_dir)
            projects = processor.scan_projects()
            
            self.batch_results_text.config(state=tk.NORMAL)
            self.batch_results_text.delete(1.0, tk.END)
            
            if projects:
                self.append_batch_results(f"扫描完成，共发现 {len(projects)} 个项目:\n")
                
                valid_count = 0
                for project in projects:
                    project_code = project.get('project_code', 'N/A')
                    project_name = project['project_name']
                    
                    if project['is_valid']:
                        valid_count += 1
                        files = list(project['files'].keys())
                        self.append_batch_results(f"✓ [{project_code}] {project_name}")
                        self.append_batch_results(f"   文件: {', '.join(files)}")
                    else:
                        self.append_batch_results(f"✗ [{project_code}] {project_name}")
                        self.append_batch_results(f"   错误: {'; '.join(project['error_messages'])}")
                
                self.append_batch_results(f"\n有效项目: {valid_count}/{len(projects)}")
                self.update_batch_status(f"扫描完成，找到 {valid_count} 个有效项目")
            else:
                self.append_batch_results("未找到任何项目")
                self.update_batch_status("未找到任何项目")
                
        except Exception as e:
            messagebox.showerror("错误", f"扫描项目时发生错误: {str(e)}")
            self.update_batch_status("扫描失败")

    def start_batch_processing(self):
        """开始批量处理"""
        input_dir = self.batch_input_dir_entry.get().strip()
        output_dir = self.batch_output_dir_entry.get().strip()
        
        if not input_dir:
            messagebox.showerror("错误", "请先选择输入目录")
            return
            
        if not output_dir:
            messagebox.showerror("错误", "请先选择输出目录")
            return

        # 确认开始处理
        if not messagebox.askyesno("确认", "确定要开始批量处理吗？这可能需要一些时间。"):
            return

        # 重置停止标志
        self.stop_processing = False

        try:
            # 禁用开始按钮，启用停止按钮
            self.batch_process_button.config(state=tk.DISABLED)
            self.batch_stop_button.config(state=tk.NORMAL)

            # 清空结果显示
            self.batch_results_text.config(state=tk.NORMAL)
            self.batch_results_text.delete(1.0, tk.END)

            # 设置进度回调
            def progress_callback(message, current, total):
                # 检查是否需要终止处理
                if self.stop_processing:
                    raise InterruptedError("用户请求终止批量处理")

                if total > 0:
                    progress = (current / total) * 100
                    self.batch_progress_var.set(progress)
                self.update_batch_status(message)
                self.append_batch_results(message)
            
            # 开始批量处理
            from batch_processor import run_batch_processing
            config_dir = str(Path(__file__).parent / "input" / "配置文件")
            
            result = run_batch_processing(input_dir, output_dir, config_dir, progress_callback)
            
            # 显示处理结果
            self.append_batch_results(f"\n批量处理完成！")
            self.append_batch_results(f"总项目数: {result['total']}")
            self.append_batch_results(f"成功处理: {result['processed']}")
            self.append_batch_results(f"处理失败: {result['failed']}")
            self.append_batch_results(f"跳过项目: {result['skipped']}")
            self.append_batch_results(f"输出目录: {result['output_dir']}")
            
            # 如果有处理失败的项目，显示详细错误信息
            if result['failed'] > 0:
                self.append_batch_results(f"\n=== 处理失败的项目详情 ===")
                
                # 读取处理报告文件获取详细错误信息
                report_file = Path(result['output_dir']) / "批量处理报告.txt"
                if report_file.exists():
                    try:
                        with open(report_file, 'r', encoding='utf-8') as f:
                            report_content = f.read()
                            
                        # 提取失败项目的信息
                        lines = report_content.split('\n')
                        in_failed_section = False
                        failed_info = []
                        
                        for line in lines:
                            if "处理失败的项目:" in line:
                                in_failed_section = True
                                continue
                            elif in_failed_section and line.strip() == "":
                                break
                            elif in_failed_section and line.strip():
                                failed_info.append(line.strip())
                        
                        if failed_info:
                            self.append_batch_results("失败项目列表:")
                            for info in failed_info:
                                self.append_batch_results(f"  {info}")
                        else:
                            self.append_batch_results("无法从报告文件中提取失败项目详情")
                            
                    except Exception as e:
                        self.append_batch_results(f"读取处理报告失败: {str(e)}")
                else:
                    self.append_batch_results("未找到处理报告文件")
            
            # 如果有跳过的项目，也显示详情
            if result['skipped'] > 0:
                self.append_batch_results(f"\n=== 跳过的项目详情 ===")
                
                # 读取处理报告文件获取跳过项目信息
                report_file = Path(result['output_dir']) / "批量处理报告.txt"
                if report_file.exists():
                    try:
                        with open(report_file, 'r', encoding='utf-8') as f:
                            report_content = f.read()
                            
                        # 提取跳过项目的信息
                        lines = report_content.split('\n')
                        in_skipped_section = False
                        skipped_info = []
                        
                        for line in lines:
                            if "跳过的项目:" in line:
                                in_skipped_section = True
                                continue
                            elif in_skipped_section and line.strip() == "":
                                break
                            elif in_skipped_section and line.strip():
                                skipped_info.append(line.strip())
                        
                        if skipped_info:
                            self.append_batch_results("跳过项目列表:")
                            for info in skipped_info:
                                self.append_batch_results(f"  {info}")
                        else:
                            self.append_batch_results("无法从报告文件中提取跳过项目详情")
                            
                    except Exception as e:
                        self.append_batch_results(f"读取处理报告失败: {str(e)}")
                else:
                    self.append_batch_results("未找到处理报告文件")
            
            # 显示成功处理的项目
            if result['processed'] > 0:
                self.append_batch_results(f"\n=== 成功处理的项目 ===")
                
                # 读取处理报告文件获取成功项目信息
                report_file = Path(result['output_dir']) / "批量处理报告.txt"
                if report_file.exists():
                    try:
                        with open(report_file, 'r', encoding='utf-8') as f:
                            report_content = f.read()
                            
                        # 提取成功项目的信息
                        lines = report_content.split('\n')
                        in_success_section = False
                        success_info = []
                        
                        for line in lines:
                            if "成功处理的项目:" in line:
                                in_success_section = True
                                continue
                            elif in_success_section and line.strip() == "":
                                break
                            elif in_success_section and line.strip():
                                success_info.append(line.strip())
                        
                        if success_info:
                            self.append_batch_results("成功项目列表:")
                            for info in success_info:
                                self.append_batch_results(f"  {info}")
                        else:
                            self.append_batch_results("无法从报告文件中提取成功项目详情")
                            
                    except Exception as e:
                        self.append_batch_results(f"读取处理报告失败: {str(e)}")
                else:
                    self.append_batch_results("未找到处理报告文件")
            
            self.batch_progress_var.set(100)
            self.update_batch_status("批量处理完成")
            
            # 显示完成对话框
            completion_message = (
                f"批量处理完成！\n\n"
                f"总项目数: {result['total']}\n"
                f"成功处理: {result['processed']}\n"
                f"处理失败: {result['failed']}\n"
                f"跳过项目: {result['skipped']}\n\n"
                f"输出目录: {result['output_dir']}"
            )
            
            if result['failed'] > 0:
                completion_message += f"\n\n注意：有{result['failed']}个项目处理失败，请查看详细错误信息。"
            
            messagebox.showinfo("完成", completion_message)
            
        except InterruptedError as e:
            # 用户终止处理
            self.update_batch_status("批量处理已终止")
            self.append_batch_results("✗ 批量处理已被用户终止")

        except Exception as e:
            error_msg = f"批量处理时发生错误: {str(e)}"
            if not self.stop_processing:  # 只有在非终止状态下才显示错误对话框
                messagebox.showerror("错误", error_msg)
            self.update_batch_status("批量处理失败")
            self.append_batch_results(f"✗ {error_msg}")
        finally:
            # 重新启用开始按钮，禁用终止按钮
            self.batch_process_button.config(state=tk.NORMAL)
            self.batch_stop_button.config(state=tk.DISABLED)
            self.stop_processing = False




class ExcelFileSelectorWithContinue(ExcelFileSelector):
    """带继续处理功能的文件选择器"""

    def __init__(self, root, callback=None):
        super().__init__(root, callback)
        self.continue_button = None
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

    def add_continue_button(self):
        """添加继续处理按钮"""
        if hasattr(self, "continue_button") and self.continue_button:
            return

        self.continue_button = ttk.Button(
            self.content_frame,
            text="继续处理",
            width=15,
            style="Accent.TButton",
            command=self.enable_continue
        )
        self.continue_button.pack(pady=10)
        self.process_button.config(state="disabled")  # 禁用开始处理按钮
        self.stop_button.config(state="disabled")  # 禁用终止按钮

    def enable_continue(self):
        """启用继续处理模式"""
        if self.continue_button:
            self.continue_button.pack_forget()
            self.continue_button = None

        self.process_button.config(state="normal")
        self.stop_button.config(state="disabled")  # 确保终止按钮被禁用
        self.status_var.set("请选择输入文件和输出位置")