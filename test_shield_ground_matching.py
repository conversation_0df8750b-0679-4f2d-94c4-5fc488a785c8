#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试屏蔽层+接地铜排的新匹配规则
"""

import pandas as pd
import sys
import os

# 添加src目录到路径
sys.path.append('src')

def test_shield_ground_matching():
    """测试屏蔽层+接地铜排的匹配逻辑"""
    
    # 模拟输入数据
    parallel_data = {
        '并线组号': ['P01_2T2D:12', 'P01_2T2D:15', 'P01_2T2D:18'],
        '颜色/线径标识1': ['屏蔽层', '屏蔽层', '屏蔽层'],
        '颜色/线径标识2': ['花(**)', '花(**)', '花(**)'],
        '设备类型1': ['端子排/屏蔽层', '端子排/屏蔽层', '端子排/屏蔽层'],
        '设备类型2': ['接地铜排/端子排', '接地铜排/端子排', '接地铜排/端子排'],
        '对应线径1': ['屏蔽层', '屏蔽层', '屏蔽层'],
        '对应线径2': [4, 4, 4]
    }
    
    parallel_df = pd.DataFrame(parallel_data)
    
    print("=== 测试数据 ===")
    print(parallel_df.to_string())
    print()
    
    # 读取压头匹配表
    try:
        sheet1 = pd.read_excel('input/配置文件/压头匹配.xlsx', sheet_name=0)
        sheet2 = pd.read_excel('input/配置文件/压头匹配.xlsx', sheet_name=1)
        
        print("=== 压头匹配表加载成功 ===")
        print(f"Sheet1 行数: {len(sheet1)}")
        print(f"Sheet2 行数: {len(sheet2)}")
        print()
        
        # 查看接地铜排的匹配规则
        ground_rules = sheet1[sheet1['物料类别'] == '接地铜排']
        print("=== 接地铜排匹配规则 ===")
        print(ground_rules.to_string())
        print()
        
        # 查看相关压头
        print("=== 相关压头信息 ===")
        
        # 地线/屏蔽层压头
        shield_terminals = sheet2[sheet2['对应线径'].str.contains('屏蔽层|地线', na=False)]
        print("屏蔽层相关压头:")
        print(shield_terminals[['压头名称', '对应线径', '并线要求', '接口类型']].to_string())
        print()
        
        # M6接口压头
        m6_terminals = sheet2[sheet2['接口类型'] == 'M6']
        print("M6接口压头:")
        print(m6_terminals[['压头名称', '对应线径', '接口类型']].to_string())
        print()
        
        # 线径为4的压头
        diameter_4_terminals = sheet2[sheet2['对应线径'] == '4.0']
        print("线径4.0的压头:")
        print(diameter_4_terminals[['压头名称', '对应线径', '并线要求', '接口类型']].to_string())
        
    except Exception as e:
        print(f"读取压头匹配表失败: {e}")
        return
    
    print("\n=== 预期匹配结果 ===")
    print("1. 并接位置（端子排）: 地线/屏蔽层压头 × 1个")
    print("2. 接地铜排另一端: M6接口压头 × 1个") 
    print("3. 屏蔽层端: 不需要压头")

if __name__ == "__main__":
    test_shield_ground_matching()
