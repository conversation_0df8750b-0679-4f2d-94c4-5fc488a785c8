# multi_core_wire_processor.py

import pandas as pd
import re
from collections import defaultdict
import logging

# 导入日志配置
from logger_config import get_main_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info

# 获取日志记录器
logger = get_main_logger()
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志


def extract_terminal_number(connection_point):
    """
    从连接点字符串中提取端号
    例如：'1-1DK1:2' -> 2
    """
    if pd.isna(connection_point):
        return None
    
    connection_str = str(connection_point)
    # 查找冒号后的数字
    match = re.search(r':(\d+)', connection_str)
    if match:
        return int(match.group(1))
    return None


def extract_device_identifier(connection_point):
    """
    从连接点字符串中提取设备标号（冒号前的部分）
    例如：'1-1DK1:2' -> '1-1DK1'
    """
    if pd.isna(connection_point):
        return None
    
    connection_str = str(connection_point)
    # 查找冒号前的部分
    if ':' in connection_str:
        return connection_str.split(':')[0]
    return connection_str


def is_consecutive_numbers(numbers):
    """
    检查数字列表是否连续
    """
    if not numbers or len(numbers) < 2:
        return False
    
    sorted_numbers = sorted(numbers)
    for i in range(1, len(sorted_numbers)):
        if sorted_numbers[i] - sorted_numbers[i-1] != 1:
            return False
    return True


def check_color_pattern(color_list):
    """
    检查颜色模式，判断是四芯线还是两芯线
    
    返回：
    - 'four_core': 四芯线（蓝($)、棕($)、黑($)、花($)各1次 或 蓝($)、棕($)、黑($)各1次）
    - 'two_core': 两芯线（蓝($)、棕($)各1次，没有黑($)、花($)）
    - None: 不符合多芯线模式
    """
    # 统计各种颜色的出现次数
    color_count = defaultdict(int)
    
    for color in color_list:
        if pd.isna(color):
            continue
        color_str = str(color)
        if '蓝($)' in color_str:
            color_count['蓝($)'] += 1
        if '棕($)' in color_str:
            color_count['棕($)'] += 1
        if '黑($)' in color_str:
            color_count['黑($)'] += 1
        if '花($)' in color_str:
            color_count['花($)'] += 1
    
    # 检查四芯线模式
    # 模式1：蓝($)、棕($)、黑($)、花($)各1次
    if (color_count['蓝($)'] == 1 and color_count['棕($)'] == 1 and 
        color_count['黑($)'] == 1 and color_count['花($)'] == 1):
        return 'four_core'
    
    # 模式2：蓝($)、棕($)、黑($)各1次
    if (color_count['蓝($)'] == 1 and color_count['棕($)'] == 1 and 
        color_count['黑($)'] == 1 and color_count['花($)'] == 0):
        return 'four_core'
    
    # 检查两芯线模式：蓝($)、棕($)各1次，没有黑($)、花($)
    if (color_count['蓝($)'] == 1 and color_count['棕($)'] == 1 and 
        color_count['黑($)'] == 0 and color_count['花($)'] == 0):
        return 'two_core'
    
    return None


def process_multi_core_wires(wiring_df, wire_length_def=None, wire_spec_def=None, material_type=None, cable_type=None):
    """
    处理多芯线统计

    参数:
    wiring_df: 屏柜配线套管表DataFrame
    wire_length_def: 线长定义文件路径
    wire_spec_def: 线材规格定义文件路径
    material_type: 线材规格类型
    cable_type: 线径选型类型

    返回:
    multi_core_df: 多芯线统计表DataFrame
    """
    try:
        log_function_start(logger, "process_multi_core_wires")
        log_data_info(logger, "输入数据", len(wiring_df))
        
        # 检查必要的列是否存在
        required_columns = ['屏柜编号', '导线起点', '颜色/线径标识', '设备类型（起点/终点）', '序号']
        missing_columns = [col for col in required_columns if col not in wiring_df.columns]
        if missing_columns:
            raise ValueError(f"缺少必要的列: {missing_columns}")
        
        multi_core_records = []
        
        # 按屏柜编号分组处理
        for cabinet_num, cabinet_group in wiring_df.groupby('屏柜编号'):
            log_process_step(logger, f"处理屏柜 {cabinet_num}")
            
            # 提取接线起点（"/"前的字符串）
            cabinet_group = cabinet_group.copy()
            cabinet_group['接线起点'] = cabinet_group['导线起点'].apply(
                lambda x: str(x).split('/')[0] if pd.notna(x) and '/' in str(x) else str(x) if pd.notna(x) else None
            )
            
            # 提取设备标号（不再提取端号，改用序号）
            cabinet_group['设备标号'] = cabinet_group['接线起点'].apply(extract_device_identifier)

            # 确保序号列为数值类型
            cabinet_group['序号'] = pd.to_numeric(cabinet_group['序号'], errors='coerce')

            # 按设备标号分组
            for device_id, device_group in cabinet_group.groupby('设备标号'):
                if pd.isna(device_id):
                    continue

                # 过滤掉序号为空的记录和屏蔽层记录
                device_group = device_group[device_group['序号'].notna()]
                device_group = device_group[~device_group['颜色/线径标识'].str.contains('屏蔽层', na=False)]
                if len(device_group) < 2:  # 至少需要2个序号才能判断多芯线
                    continue

                # 按序号排序
                device_group = device_group.sort_values('序号')

                # 查找连续的序号子组
                sequence_numbers = device_group['序号'].tolist()
                colors = device_group['颜色/线径标识'].tolist()

                # 查找多芯线模式，避免重复记录
                consecutive_groups = []
                used_sequences = set()  # 记录已经使用的序号，避免重复

                # 优先查找4芯线模式（连续4个序号）
                for start_idx in range(len(sequence_numbers) - 3):
                    if (sequence_numbers[start_idx + 1] == sequence_numbers[start_idx] + 1 and
                        sequence_numbers[start_idx + 2] == sequence_numbers[start_idx] + 2 and
                        sequence_numbers[start_idx + 3] == sequence_numbers[start_idx] + 3):

                        # 检查这些序号是否已经被使用
                        group_sequences = sequence_numbers[start_idx:start_idx + 4]
                        if any(s in used_sequences for s in group_sequences):
                            continue

                        group_colors = colors[start_idx:start_idx + 4]
                        wire_type = check_color_pattern(group_colors)
                        if wire_type == 'four_core':
                            consecutive_groups.append((group_sequences, group_colors))
                            used_sequences.update(group_sequences)

                # 然后查找3芯线模式（连续3个序号）
                for start_idx in range(len(sequence_numbers) - 2):
                    if (sequence_numbers[start_idx + 1] == sequence_numbers[start_idx] + 1 and
                        sequence_numbers[start_idx + 2] == sequence_numbers[start_idx] + 2):

                        # 检查这些序号是否已经被使用
                        group_sequences = sequence_numbers[start_idx:start_idx + 3]
                        if any(s in used_sequences for s in group_sequences):
                            continue

                        group_colors = colors[start_idx:start_idx + 3]
                        wire_type = check_color_pattern(group_colors)
                        if wire_type == 'four_core':  # 3个颜色的四芯线（蓝、棕、黑）
                            consecutive_groups.append((group_sequences, group_colors))
                            used_sequences.update(group_sequences)

                # 最后查找2芯线模式（连续2个序号）
                for start_idx in range(len(sequence_numbers) - 1):
                    if sequence_numbers[start_idx + 1] == sequence_numbers[start_idx] + 1:

                        # 检查这些序号是否已经被使用
                        group_sequences = sequence_numbers[start_idx:start_idx + 2]
                        if any(s in used_sequences for s in group_sequences):
                            continue

                        group_colors = colors[start_idx:start_idx + 2]
                        wire_type = check_color_pattern(group_colors)
                        if wire_type == 'two_core':
                            consecutive_groups.append((group_sequences, group_colors))
                            used_sequences.update(group_sequences)

                # 检查每个连续组是否符合多芯线模式
                for group_sequences, group_colors in consecutive_groups:
                    wire_type = check_color_pattern(group_colors)

                    if wire_type:
                        # 获取该组对应的记录（基于序号）
                        group_records = device_group[device_group['序号'].isin(group_sequences)]

                        # 获取设备类型（取第一条记录的设备类型）
                        device_type = group_records['设备类型（起点/终点）'].iloc[0]

                        # 获取导线起点和终点（取第一条记录的起点和终点）
                        wire_start = group_records['导线起点'].iloc[0] if '导线起点' in group_records.columns else ''
                        wire_end = group_records['导线终点'].iloc[0] if '导线终点' in group_records.columns else ''

                        # 确定对应线径
                        if wire_type == 'four_core':
                            wire_diameter = 0.12
                            color_identifier = '四芯线'
                        else:  # two_core
                            wire_diameter = 0.3
                            color_identifier = '两芯线'

                        multi_core_records.append({
                            '屏柜编号': cabinet_num,
                            '设备类型': device_type,
                            '颜色/线径标识': color_identifier,
                            '对应线径': wire_diameter,
                            '导线起点': wire_start,
                            '导线终点': wire_end,
                            '单根长度': None,  # 待后续填充
                            '对应编码': None   # 待后续填充
                        })

                        log_process_step(logger, f"发现{color_identifier}",
                                       f"屏柜={cabinet_num}, 设备={device_id}, 序号={group_sequences}, 起点={wire_start}")
        
        # 创建多芯线统计表
        multi_core_df = pd.DataFrame(multi_core_records)
        log_data_info(logger, "多芯线统计结果", len(multi_core_df))

        # 为多芯线统计表添加单根长度和对应编码
        if not multi_core_df.empty:
            log_process_step(logger, "为多芯线统计表添加单根长度和对应编码")

            # 添加单根长度
            if wire_length_def:
                multi_core_df = add_multi_core_wire_length(multi_core_df, wire_length_def)

            # 添加对应编码
            if wire_spec_def and material_type and cable_type:
                multi_core_df = add_multi_core_wire_code(multi_core_df, wire_spec_def, material_type, cable_type)

        log_function_end(logger, "process_multi_core_wires")
        return multi_core_df

    except Exception as e:
        log_function_error(logger, "process_multi_core_wires", str(e))
        raise


def get_multi_core_wire_code_info(multi_core_df, wire_spec_def, material_type, cable_type):
    """
    获取多芯线编码信息，用于BOM处理

    参数:
    multi_core_df: 多芯线统计DataFrame
    wire_spec_def: 线材规格定义文件路径
    material_type: 线材规格类型
    cable_type: 线径选型类型

    返回:
    dict: 编码信息字典
    """
    try:
        log_function_start(logger, "get_multi_core_wire_code_info")

        code_info = {}

        if multi_core_df.empty or not wire_spec_def:
            return code_info

        # 读取线材规格定义文件
        try:
            # 读取编码映射表（第一个工作表）
            spec_df = pd.read_excel(wire_spec_def, sheet_name=0)
            # 读取物料信息表（Sheet3）
            material_info_df = pd.read_excel(wire_spec_def, sheet_name='Sheet3')
        except Exception as e:
            log_function_error(logger, "读取线材规格定义文件失败", str(e))
            return code_info

        # 检查物料信息表的列名
        log_process_step(logger, "物料信息表列名", f"{list(material_info_df.columns)}")

        # 为每条多芯线记录收集编码信息
        for idx, row in multi_core_df.iterrows():
            wire_code = row.get('对应编码', '')
            if not wire_code:
                continue

            wire_diameter = row['对应线径']
            wire_type = row['颜色/线径标识']

            # 在物料信息表中查找匹配的记录
            code_match = material_info_df[material_info_df['物料编码'] == wire_code]

            if not code_match.empty:
                match_row = code_match.iloc[0]
                code_info[wire_code] = {
                    '物料名称': match_row.get('物料名称', ''),
                    '损耗率': match_row.get('损耗率', 0.0),
                    '最小有效值': match_row.get('最小有效值', 0.0),
                    '最小有效值小数位数': 2  # 默认值，因为Sheet3中没有这个字段
                }
                log_process_step(logger, f"多芯线编码信息收集", f"编码={wire_code}, 物料名称={match_row.get('物料名称', '')}")
            else:
                # 如果在物料信息表中找不到，设置默认值
                code_info[wire_code] = {
                    '物料名称': f'多芯线-{wire_type}',
                    '损耗率': 0.0,
                    '最小有效值': 0.001,
                    '最小有效值小数位数': 2
                }
                log_process_step(logger, f"多芯线编码信息默认", f"编码={wire_code}, 使用默认值")

        log_data_info(logger, "多芯线编码信息", len(code_info))
        log_function_end(logger, "get_multi_core_wire_code_info")
        return code_info

    except Exception as e:
        log_function_error(logger, "get_multi_core_wire_code_info", str(e))
        return {}
        
    except Exception as e:
        log_function_error(logger, "process_multi_core_wires", str(e))
        raise


def get_multi_core_wire_indices(wiring_df):
    """
    获取多芯线对应的原始数据索引，用于从导线统计中排除
    
    参数:
    wiring_df: 屏柜配线套管表DataFrame
    
    返回:
    set: 多芯线对应的原始数据索引集合
    """
    try:
        log_function_start(logger, "get_multi_core_wire_indices")
        
        multi_core_indices = set()
        
        # 按屏柜编号分组处理
        for cabinet_num, cabinet_group in wiring_df.groupby('屏柜编号'):
            # 提取接线起点（"/"前的字符串）
            cabinet_group = cabinet_group.copy()
            cabinet_group['接线起点'] = cabinet_group['导线起点'].apply(
                lambda x: str(x).split('/')[0] if pd.notna(x) and '/' in str(x) else str(x) if pd.notna(x) else None
            )
            
            # 提取设备标号（不再提取端号，改用序号）
            cabinet_group['设备标号'] = cabinet_group['接线起点'].apply(extract_device_identifier)

            # 确保序号列为数值类型
            cabinet_group['序号'] = pd.to_numeric(cabinet_group['序号'], errors='coerce')

            # 按设备标号分组
            for device_id, device_group in cabinet_group.groupby('设备标号'):
                if pd.isna(device_id):
                    continue

                # 过滤掉序号为空的记录和屏蔽层记录
                device_group = device_group[device_group['序号'].notna()]
                device_group = device_group[~device_group['颜色/线径标识'].str.contains('屏蔽层', na=False)]
                if len(device_group) < 2:  # 至少需要2个序号才能判断多芯线
                    continue

                # 按序号排序
                device_group = device_group.sort_values('序号')

                # 查找多芯线模式，避免重复记录
                sequence_numbers = device_group['序号'].tolist()
                colors = device_group['颜色/线径标识'].tolist()
                used_sequences = set()  # 记录已经使用的序号，避免重复

                # 优先查找4芯线模式（连续4个序号）
                for start_idx in range(len(sequence_numbers) - 3):
                    if (sequence_numbers[start_idx + 1] == sequence_numbers[start_idx] + 1 and
                        sequence_numbers[start_idx + 2] == sequence_numbers[start_idx] + 2 and
                        sequence_numbers[start_idx + 3] == sequence_numbers[start_idx] + 3):

                        # 检查这些序号是否已经被使用
                        group_sequences = sequence_numbers[start_idx:start_idx + 4]
                        if any(s in used_sequences for s in group_sequences):
                            continue

                        group_colors = colors[start_idx:start_idx + 4]
                        wire_type = check_color_pattern(group_colors)
                        if wire_type == 'four_core':
                            # 将这些索引添加到集合中
                            group_indices = device_group.iloc[start_idx:start_idx + 4].index.tolist()
                            multi_core_indices.update(group_indices)
                            used_sequences.update(group_sequences)

                # 然后查找3芯线模式（连续3个序号）
                for start_idx in range(len(sequence_numbers) - 2):
                    if (sequence_numbers[start_idx + 1] == sequence_numbers[start_idx] + 1 and
                        sequence_numbers[start_idx + 2] == sequence_numbers[start_idx] + 2):

                        # 检查这些序号是否已经被使用
                        group_sequences = sequence_numbers[start_idx:start_idx + 3]
                        if any(s in used_sequences for s in group_sequences):
                            continue

                        group_colors = colors[start_idx:start_idx + 3]
                        wire_type = check_color_pattern(group_colors)
                        if wire_type == 'four_core':  # 3个颜色的四芯线（蓝、棕、黑）
                            # 将这些索引添加到集合中
                            group_indices = device_group.iloc[start_idx:start_idx + 3].index.tolist()
                            multi_core_indices.update(group_indices)
                            used_sequences.update(group_sequences)

                # 最后查找2芯线模式（连续2个序号）
                for start_idx in range(len(sequence_numbers) - 1):
                    if sequence_numbers[start_idx + 1] == sequence_numbers[start_idx] + 1:

                        # 检查这些序号是否已经被使用
                        group_sequences = sequence_numbers[start_idx:start_idx + 2]
                        if any(s in used_sequences for s in group_sequences):
                            continue

                        group_colors = colors[start_idx:start_idx + 2]
                        wire_type = check_color_pattern(group_colors)
                        if wire_type == 'two_core':
                            # 将这些索引添加到集合中
                            group_indices = device_group.iloc[start_idx:start_idx + 2].index.tolist()
                            multi_core_indices.update(group_indices)
                            used_sequences.update(group_sequences)
        
        log_data_info(logger, "多芯线索引", len(multi_core_indices))
        log_function_end(logger, "get_multi_core_wire_indices")
        return multi_core_indices

    except Exception as e:
        log_function_error(logger, "get_multi_core_wire_indices", str(e))
        raise


def add_multi_core_wire_length(multi_core_df, wire_length_def):
    """
    为多芯线统计表添加单根长度

    参数:
    multi_core_df: 多芯线统计DataFrame
    wire_length_def: 线长定义文件路径

    返回:
    添加单根长度后的DataFrame
    """
    try:
        log_function_start(logger, "add_multi_core_wire_length")

        if multi_core_df.empty:
            return multi_core_df

        # 读取线长定义文件
        length_df = pd.read_excel(wire_length_def)

        # 检查线长定义文件的列名
        log_process_step(logger, "线长定义文件列名", f"{list(length_df.columns)}")

        # 创建设备组合到长度的映射字典（与wire_length_processor.py中的逻辑一致）
        from collections import defaultdict
        length_map = defaultdict(lambda: None)

        # 创建双向映射：A/B和B/A都映射到同一个长度
        for _, row in length_df.iterrows():
            a = row['相对位置A']
            b = row['相对位置B']
            length = row['预估长度']

            # 创建两种键
            key1 = f"{a}/{b}"
            key2 = f"{b}/{a}"

            length_map[key1] = length
            length_map[key2] = length

        # 为每条多芯线记录匹配长度
        for idx, row in multi_core_df.iterrows():
            device_type = row['设备类型']

            # 使用与普通导线统计相同的匹配逻辑
            matched_length = length_map.get(device_type, None)

            if matched_length is not None:
                multi_core_df.at[idx, '单根长度'] = matched_length
                log_process_step(logger, f"多芯线长度匹配", f"设备类型={device_type}, 长度={matched_length}")
            else:
                # 如果没有匹配，设置默认长度
                multi_core_df.at[idx, '单根长度'] = 2.5  # 默认长度
                log_process_step(logger, f"多芯线长度默认", f"设备类型={device_type}, 默认长度=2.5")

        log_function_end(logger, "add_multi_core_wire_length")
        return multi_core_df

    except Exception as e:
        log_function_error(logger, "add_multi_core_wire_length", str(e))
        return multi_core_df


def add_multi_core_wire_code(multi_core_df, wire_spec_def, material_type, cable_type):
    """
    为多芯线统计表添加对应编码

    参数:
    multi_core_df: 多芯线统计DataFrame
    wire_spec_def: 线材规格定义文件路径
    material_type: 线材规格类型
    cable_type: 线径选型类型

    返回:
    添加对应编码后的DataFrame
    """
    try:
        log_function_start(logger, "add_multi_core_wire_code")

        if multi_core_df.empty:
            return multi_core_df

        # 读取线材规格定义文件
        try:
            spec_df = pd.read_excel(wire_spec_def, sheet_name=material_type)
        except ValueError as e:
            log_process_step(logger, f"工作表不存在", f"工作表名={material_type}, 错误={str(e)}")
            # 尝试读取第一个工作表
            try:
                spec_df = pd.read_excel(wire_spec_def, sheet_name=0)
                log_process_step(logger, "使用第一个工作表", "成功")
            except Exception as e2:
                log_function_error(logger, "读取线材规格定义文件失败", str(e2))
                return multi_core_df

        # 检查线材规格定义文件的列名
        log_process_step(logger, "线材规格定义文件列名", f"{list(spec_df.columns)}")

        # 尝试找到相关的列
        diameter_col = None
        wire_type_col = None
        code_col = None

        for col in spec_df.columns:
            if '线径' in str(col) and '对应' in str(col):
                diameter_col = col
            if '线材类型' in str(col) or '类型' in str(col):
                wire_type_col = col
            if material_type in str(col):  # 根据material_type找到对应的编码列
                code_col = col

        if not diameter_col or not wire_type_col or not code_col:
            log_process_step(logger, "线材规格定义文件列名不匹配", f"线径列={diameter_col}, 类型列={wire_type_col}, 编码列={code_col}")
            # 如果找不到匹配的列，设置空编码
            multi_core_df['对应编码'] = ''
            return multi_core_df

        # 为每条多芯线记录匹配编码
        for idx, row in multi_core_df.iterrows():
            wire_diameter = row['对应线径']
            wire_type = row['颜色/线径标识']

            # 在线材规格定义中查找匹配的线径和类型
            match_condition = (spec_df[diameter_col] == wire_diameter) & (spec_df[wire_type_col] == wire_type)
            matched_records = spec_df[match_condition]

            if not matched_records.empty:
                code = matched_records.iloc[0][code_col]
                multi_core_df.at[idx, '对应编码'] = code
                log_process_step(logger, f"多芯线编码匹配", f"线径={wire_diameter}, 类型={wire_type}, 编码={code}")
            else:
                # 如果精确匹配失败，尝试只按线径匹配
                diameter_match = spec_df[spec_df[diameter_col] == wire_diameter]
                if not diameter_match.empty:
                    code = diameter_match.iloc[0][code_col]
                    multi_core_df.at[idx, '对应编码'] = code
                    log_process_step(logger, f"多芯线编码按线径匹配", f"线径={wire_diameter}, 编码={code}")
                else:
                    multi_core_df.at[idx, '对应编码'] = ''
                    log_process_step(logger, f"多芯线编码未匹配", f"线径={wire_diameter}, 类型={wire_type}")

        log_function_end(logger, "add_multi_core_wire_code")
        return multi_core_df

    except Exception as e:
        log_function_error(logger, "add_multi_core_wire_code", str(e))
        return multi_core_df
