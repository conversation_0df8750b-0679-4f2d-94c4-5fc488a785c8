# excel_formatter.py (修改后，移出版本检查功能)

import openpyxl
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def auto_adjust_columns_width(worksheet):
    """
    自动调整列宽以适应内容
    :param worksheet: openpyxl 工作表对象
    """
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter

        # 计算每列的最大字符长度
        for cell in column:
            try:
                cell_value = str(cell.value) if cell.value is not None else ""
                if len(cell_value) > max_length:
                    max_length = len(cell_value)
            except:
                pass

        # 设置列宽（+2作为边距）
        adjusted_width = max_length + 2
        worksheet.column_dimensions[column_letter].width = adjusted_width


def save_dataframe_to_excel(df, file_path, sheet_name):
    """
    将DataFrame保存到Excel文件并自动调整列宽
    :param df: 要保存的DataFrame
    :param file_path: 文件路径
    :param sheet_name: 工作表名称
    """
    # 验证文件路径
    if not file_path:
        raise ValueError("文件路径不能为空")
    
    # 确保输出目录存在
    output_dir = os.path.dirname(file_path)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 创建或加载工作簿
    try:
        if os.path.exists(file_path):
            wb = openpyxl.load_workbook(file_path)
        else:
            wb = openpyxl.Workbook()
            # 删除默认创建的工作表
            if wb.active:
                wb.remove(wb.active)
    except Exception as e:
        # 文件损坏或其他问题，创建新工作簿
        # print(f"加载工作簿失败，创建新工作簿: {e}")
        wb = openpyxl.Workbook()
        if wb.active:
            wb.remove(wb.active)

    # 如果工作表已经存在，则删除
    if sheet_name in wb.sheetnames:
        wb.remove(wb[sheet_name])

    # 创建工作表
    ws = wb.create_sheet(title=sheet_name)

    # 将DataFrame写入工作表
    for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=True), 1):
        for c_idx, value in enumerate(row, 1):
            ws.cell(row=r_idx, column=c_idx, value=value)

    # 自动调整列宽
    auto_adjust_columns_width(ws)

    # 保存工作簿
    try:
        wb.save(file_path)
        # print(f"成功保存工作表 '{sheet_name}' 到文件: {file_path}")
    except Exception as e:
        # print(f"保存文件失败: {e}")
        raise